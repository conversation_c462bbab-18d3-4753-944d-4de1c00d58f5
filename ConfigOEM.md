# 配置OEM
正式环境 
1.更改uni.scss中主题色 $config-color

2.配置src/config文件
新增配置相关的内容
示例：
    import {ConfigInterface} from './index';
    export const yzgp: ConfigInterface = {
    // 请求地址
    baseApi: "https://blzng-api.zlxx365.com",
    // 请求key
    appKey: "blzng",
    // 请求秘钥
    secret: "Blzngqwe123",
    // oss地址
    ossApi: "https://blzng-img.zlxx365.com",
    // 协议域名
    agreementDomain: "https://blzng-s.zlxx365.com",
    // 微信小程序appid
    appid: 'wx6302929c09fe55cf',
    // 主色调
    color: "#1871ee",
    // 品牌名称
    brandName: "便利智能柜",
    };

3.在src/config/index.ts下根据配置名进行判断

4.配置页面显示图片

5.在页面中根据配置名进行图片显示判断
全局搜索 process.env.VUE_APP_CHANNEL

6.配置package.json打包命令
"build:mp-weixin:配置名": "cross-env NODE_ENV=production CHANNEL=配置名 APPID=配置小程序ID node build/modifyManifestStart.js && cross-env NODE_ENV=production CHANNEL=配置名 APPID=配置小程序ID UNI_PLATFORM=mp-weixin vue-cli-service uni-build && cross-env CHANNEL=配置名 APPID=配置小程序ID node build/modifyManifest.js",

测试环境
1.更改uni.scss中主题色 $config-color

2.配置src/config文件
新增配置相关的内容
示例：
import {ConfigInterface} from './index';
export const yzgp: ConfigInterface = {
// 请求地址
baseApi: "https://blzng-api.zlxx365.com",
// 请求key
appKey: "blzng",
// 请求秘钥
secret: "Blzngqwe123",
// oss地址
ossApi: "https://blzng-img.zlxx365.com",
// 协议域名
agreementDomain: "https://blzng-s.zlxx365.com",
// 微信小程序appid
appid: 'wx6302929c09fe55cf',
// 主色调
color: "#1871ee",
// 品牌名称
brandName: "便利智能柜",
};

3.在src/config/index.ts下根据配置名进行判断

4.配置页面显示图片

5.在页面中根据配置名进行图片显示判断
全局搜索 process.env.VUE_APP_CHANNEL

6.单独手动更改 .env.development配置文件
