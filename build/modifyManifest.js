const fs = require('fs')
const configJsonPath = `dist/build/mp-weixin/project.config.json`

fs.readFile(configJsonPath,
    function (err, data) {
        if (err) {
            console.error(err);
        } else {
            var _data = JSON.parse(data.toString())
            _data.appid = process.env.APPID
            _data = JSON.stringify(_data);
            // 写入
            fs.writeFile(configJsonPath, _data, {
                encoding: "utf-8"
            }, function (err) {
                if (err) {
                    console.log('写入失败', err);
                } else {
                    console.log('写入成功');
                }
            });
        }
    }
)
