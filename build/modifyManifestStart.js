const fs = require('fs')
console.log(process.env,'8888888888')

const configJsonPath = `.env.production`
fs.readFile(configJsonPath,
    function (err, data) {
        if (err) {
            console.error(err);
        } else {
            var _data = data.toString()
            _data=`VUE_APP_CHANNEL = ${process.env.CHANNEL}`
            // 写入
            fs.writeFile(configJsonPath, _data, {
                encoding: "utf-8"
            }, function (err) {
                if (err) {
                    console.log('写入失败', err);
                } else {
                    console.log('写入成功');
                }
            });
        }
    }
)