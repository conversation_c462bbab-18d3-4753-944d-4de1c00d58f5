{"name": "cabinet-uniapp-customer", "version": "0.1.0", "private": true, "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production CHANNEL=xmzng UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-alipay:yzgp": "cross-env NODE_ENV=production CHANNEL=yzgp UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production  CHANNEL=xmzng APPID=wx8dff26368aa613e0 node build/modifyManifestStart.js && cross-env NODE_ENV=production CHANNEL=xmzng APPID=wx8dff26368aa613e0 UNI_PLATFORM=mp-weixin vue-cli-service uni-build && cross-env CHANNEL=xmzng APPID=wx8dff26368aa613e0 node build/modifyManifest.js", "build:mp-weixin:yzgp": "cross-env NODE_ENV=production ENV=yzgp CHANNEL=yzgp APPID=wx6302929c09fe55cf node build/modifyManifestStart.js && cross-env NODE_ENV=production ENV=yzgp CHANNEL=yzgp APPID=wx6302929c09fe55cf UNI_PLATFORM=mp-weixin vue-cli-service uni-build && cross-env CHANNEL=yzgp APPID=wx6302929c09fe55cf node build/modifyManifest.js", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i"}, "dependencies": {"@dcloudio/uni-app-plus": "^3.0.0-alpha-3040820220428001", "@dcloudio/uni-cli-i18n": "^2.0.0-32920211122002", "@dcloudio/uni-h5": "^2.0.0-32920211122002", "@dcloudio/uni-helper-json": "^1.0.11", "@dcloudio/uni-mp-alipay": "^2.0.0-32920211122002", "@dcloudio/uni-mp-vue": "^2.0.0-32920211122002", "@dcloudio/uni-mp-weixin": "^2.0.0-32920211122002", "@dcloudio/uni-stat": "^2.0.0-32920211122002", "@vue/shared": "^3.0.5", "core-js": "^3.8.2", "crypto-js": "^4.1.1", "flyio": "^0.6.2", "less": "^3.12.2", "less-loader": "^7.2.1", "reflect-metadata": "^0.1.13", "regenerator-runtime": "^0.12.1", "sass": "^1.32.4", "sass-loader": "^10.0.2", "uview-ui": "^2.0.19", "vue": "^2.6.14", "vue-class-component": "^6.3.2", "vue-property-decorator": "^8.0.0", "vuex": "^3.6.0"}, "devDependencies": {"@babel/plugin-syntax-typescript": "^7.2.0", "@dcloudio/types": "*", "@dcloudio/uni-automator": "^2.0.0-32920211122002", "@dcloudio/uni-cli-shared": "^2.0.0-32920211122002", "@dcloudio/uni-migration": "^2.0.0-32920211122002", "@dcloudio/uni-template-compiler": "^2.0.0-32920211122002", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0-32920211122002", "@dcloudio/vue-cli-plugin-uni": "^2.0.0-32920211122002", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0-32920211122002", "@dcloudio/webpack-uni-mp-loader": "^2.0.0-32920211122002", "@dcloudio/webpack-uni-pages-loader": "^2.0.0-32920211122002", "@types/crypto-js": "^4.1.0", "@vue/cli-plugin-babel": "^4.5.10", "@vue/cli-plugin-typescript": "^4.5.10", "@vue/cli-service": "^4.5.10", "babel-plugin-import": "^1.13.3", "cross-env": "^7.0.3", "jest": "^25.4.0", "mini-types": "*", "miniprogram-api-typings": "^3.2.2", "postcss-comment": "^2.0.0", "typescript": "^3.0.0", "vue-template-compiler": "^2.6.11", "webpack-cli": "^4.9.2"}, "browserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}}