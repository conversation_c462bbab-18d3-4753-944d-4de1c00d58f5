<script lang="ts">
import {Component, Vue} from 'vue-property-decorator'
import {Cache} from '@/utils'
@Component({
  mpType: 'app'
})
export default class App extends Vue {
  onLaunch(e: any) {
    if(e?.query?.key1 || e?.query?.cabinetLocationCode){
      Cache.setItem('launchOptions', e)
    }
    // const user = Cache.getItem('user')
    // if (user != undefined) {
    //   mockLogin({customerId :user.id}).then((data) => {
    //     let a = {}
    //     // @ts-ignore
    //     a[data.tokenName] = data.tokenValue
    //     Cache.setItem('SYS_TOKEN', a)
    //     const time = new Date().getTime()
    //     Cache.setItem('last_login_time', time)
    //   })
    // }
    /* #ifdef MP-BAIDU || MP-WEIXIN */
    const updateManager = uni.getUpdateManager()

    updateManager.onCheckForUpdate((res) => {
      // 请求完新版本信息的回调
    })

    updateManager.onUpdateReady(() => {
      uni.showModal({
        title: '更新提示',
        content: '新版本已经准备好',
        success(res) {
          updateManager.applyUpdate()
        }
      })
    })

    updateManager.onUpdateFailed(function (res) {
      // 新的版本下载失败
    })
    /* #endif */
  }

  onShow(e: any) {
    if (process.env.VUE_APP_CHANNEL === 'yzgp') {
      uni.setTabBarItem({
        index: 0,
        text: '首页',
        iconPath: 'static/img/index.png',
        selectedIconPath: 'static/imgConfig/indexConfig-active.png'
      })
      uni.setTabBarItem({
        index: 1,
        text: '查询',
        iconPath: 'static/img/search.png',
        selectedIconPath: 'static/imgConfig/searchConfig-active.png'
      })
      uni.setTabBarItem({
        index: 2,
        text: '我的',
        iconPath: 'static/img/my.png',
        selectedIconPath: 'static/imgConfig/myConfig-active.png'
      })
    }
    console.log('App Show')
  }

  onHide() {
    console.log('App Hide')
  }
}
</script>

<style lang="scss">
/*每个页面公共css */
@import './styles/app.scss';
</style>
