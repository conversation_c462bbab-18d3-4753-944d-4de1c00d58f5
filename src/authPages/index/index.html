<view class="index">
  <view class="icon">
    <img :src="getImage('logo')" />
  </view>
  <button class="btn" @click="authToHomePage">登录</button>
  <view class="auth1" @click="agreeAuth">
    <text
      class="iconfont icons"
      :class="isAgree ? 'icon-xuanze05 color-active': 'icon-xuanze04'"
    ></text>
    我已阅读并同意
    <!--            <span class="color-blue" @click="goDoc('xy')">《{{ BrandName }}服务协议》</span >和-->
    <text class="color-active" @click.stop="goDoc('ys')">《隐私政策》</text>
  </view>
  <u-popup
    :show="isShowAuthMobile"
    mode="bottom"
    round="30"
    :closeable="true"
    @close="this.isShowAuthMobile = false"
  >
    <view class="popup-content">
      <view class="detail-title">授权手机号</view>
      <view class="content">
        <view class="message">
          <text class="label">申请获取您的手机号</text>
          <text class="normal"
            >为了更好的体验{{ BrandName }}小程序，{{ BrandName
            }}申请获取您的手机号，您的信息不会用于商业途径。</text
          >
        </view>
      </view>
      <view class="btn-view">
        <!-- 微信小程序环境 -->
        <button
          class="btn2 auth2"
          open-type="getPhoneNumber"
          @getphonenumber="throttle(getPhoneNumber, $event, 1500)"
          v-if="!isAlipay()"
        >
          立即授权
        </button>
        <!-- 支付宝小程序环境 -->
        <button
          class="btn2 auth2"
          open-type="getAuthorize" 
          @getAuthorize="throttle(getPhoneNumber, $event, 1500)" 
          @error="onAuthError" 
          scope='phoneNumber'
          v-if="isAlipay()">
          立即授权
        </button>
      </view>
    </view>
  </u-popup>
</view>
