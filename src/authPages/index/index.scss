.index {
  position: relative;
  width: 750upx;
  align-items: center;
  text-align: center;
  .icon {
    width: 750upx;
    height: 560upx;
    display: flex;
    justify-content: center;
    align-items: center;

    image {
      margin-top: 165upx;
      width: 192upx;
      height: 192upx;
    }
  }

  .btn {
    width: 690upx;
    margin-top: 200upx;
    height: 80upx;
    line-height: 80upx;
    color: #ffffff;
    background-color: $config-color;
    display: inline-block;
  }

  .auth1 {
    width: 690upx;
    margin: 40upx 40upx;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    line-height: 44upx;
  }


  .popup-content {
    background-color: #ffffff;
    padding: 30upx;
    position: relative;
    min-height: 500upx;
    box-sizing: border-box;

    .detail-title {
      font-size: 36upx;
      font-weight: 600;
      margin-bottom: 30upx;
    }

    .content {
      .message {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 100upx;

        .label {
          font-size: $uni-font-size-max;
        }

        .normal {
          font-size: $uni-font-size-min;
          color: #c5c5c5;
          margin-top: 10upx;
          text-align: center;
        }
      }
    }

    .btn-view {
      margin-top: 100upx;
      margin-left: 0;
      box-sizing: border-box;
      width: 690upx;
      .btn2 {
        height: 90upx;
        line-height: 90upx;
        border-radius: 5upx;
        text-align: center;
        font-size: $uni-font-size-sm;
      }

      .auth2 {
        color: #ffffff;
        background-color: $uni-text-color-global;
        margin-bottom: 40upx;
      }

      .toLogin {
        background-color: #ffffff;
        border: 1px solid $uni-text-color-global;
        color: $uni-text-color-global;
        margin-bottom: 40upx;
      }
    }
  }

}

.icons {
  font-size: 38upx;
  margin-right: 12upx;
  color: #999999
}

.color-blue {
  color: #3087D7;
}

.color-active {
  color: $config-color;
}

