<script lang="ts">
import 'reflect-metadata';
import {Component, Mixins} from 'vue-property-decorator';
import {C<PERSON>, Modal} from '@/utils';
import {bindPhone, checkExpireTime, getLoginCode, loginByWechat, removeOuter} from '@/utils/core/uni';
import {bindGzh, getUserInfo} from '@/model/login';
import {CommonMixins} from "@/mixins/commonMixin";
import {userDto, wechatPhoneDto} from "@/utils/types";
import {ImageMixins} from "@/mixins/ImageMixins";
import config from "@/utils/config";

@Component({
  name: 'Index'
})
export default class Index extends Mixins(CommonMixins, ImageMixins) {
  // private type: string = ''
  private isAgree: boolean = false
  private cabinetCode: string = ''
  private cabinetLocationId: string = ''
  private scanType: string = ''
  private gzhOpenId: string = ''
  private gzhAppId: string = ''
  private code: string = ''
  private isShowAuthMobile: boolean = false
  private user: userDto = {
    id: '',
    phone: ''
  }
  private BrandName=config.BrandName

  goDoc() {
    let url=`/pages/main/webView?url=${config.agreement}/#/privacyProtocol`
    console.log(url)
    uni.navigateTo({url})
  }

  isAlipay() {
        return uni.getSystemInfoSync().app === 'alipay'
    }

  agreeAuth() {
    this.isAgree = !this.isAgree
  }

  async getPhoneNumber(data: wechatPhoneDto) {
    try {
      if (data.detail.errMsg === 'getPhoneNumber:ok' || this.isAlipay()) {
      console.log('进入支付宝' ,data)
      const res: any = await bindPhone(data)
      if (res != null) {
        this.isShowAuthMobile = false
        Modal.toast('手机号授权成功')
        await this.getUserInfo()
      } else {
                Modal.toast('手机号授权失败，请重试')
            }
    }
    } catch (error) {
        console.error('手机号授权失败:', error)
        Modal.toast('手机号授权失败，请重试')
    }
  }

  onAuthError() {
    Modal.toast('手机号授权失败，请重试')
  }

  async authToHomePage() {
    if (this.isAgree) {
      const res: any = await loginByWechat();
      if (res != null && res.isLogin) {
        await this.getUserInfo()
        if (this.gzhOpenId && this.gzhAppId) {
          await this.binGzhId()
        }
        Modal.toast('授权用户登录成功')
      }
    } else {
      Modal.toast('请阅读并同意下方用户协议')
    }
  }

  async binGzhId() {
    const info = {
      gzhAppId: this.gzhAppId,
      gzhOpenId: this.gzhOpenId
    }
    await bindGzh(info);
  }

  async getUserInfo() {

    const user: any = await getUserInfo();
    this.user = user
    Cache.setItem('user', user)
    if (user.phone != '' && user.phone != null) {
      if ((this.cabinetCode != '' && this.cabinetCode != null) || ((this.cabinetLocationId != '' && this.cabinetLocationId != null))) {
        let isOverTime: boolean = checkExpireTime()
        if (!isOverTime) {
          removeOuter()
          const info = {
            cabinetLocationCode: this.cabinetCode,
            cabinetLocationId: this.cabinetLocationId
          }
          if (this.scanType && this.scanType + '' == '3') {
            uni.redirectTo({url: '/homePages/receive/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
          } else if (this.scanType && this.scanType + '' == '2') {
            uni.redirectTo({url: '/homePages/send/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
          }
          {
            uni.switchTab({url: '/pages/home/<USER>/index'})
            removeOuter()
          }
          return
        }
      }
      uni.switchTab({url: '/pages/home/<USER>/index'})
    } else {
      this.isShowAuthMobile = true
    }
  }

  onLoad() {
    const cabinetCode = Cache.getItem('cabinetLocationCode')
    const scanType = Cache.getItem('scanType')
    const cabinetLocationId = Cache.getItem('cabinetLocationId')
    const gzhOpenId = Cache.getItem('gzhOpenId')
    const gzhAppId = Cache.getItem('gzhAppId')
    if (cabinetCode != null && cabinetCode != '') {
      Cache.removeItem('cabinetLocationCode')
      this.cabinetCode = cabinetCode
    }
    if (scanType != null && scanType != '') {
      Cache.removeItem('scanType')
      this.scanType = scanType
    }
    if (cabinetLocationId != null && cabinetLocationId != '') {
      Cache.removeItem('cabinetLocationId')
      this.cabinetLocationId = cabinetLocationId
    }
    if (gzhOpenId != null && gzhOpenId != '') {
      Cache.removeItem('gzhOpenId')
      this.gzhOpenId = gzhOpenId
    }
    if (gzhAppId != null && gzhAppId != '') {
      Cache.removeItem('gzhAppId')
      this.gzhAppId = gzhAppId
    }
  }

  async onShow() {
    this.code = await getLoginCode()
  }
}
</script>

<template src="./index.html"></template>
<style src="./index.scss" lang="scss" scoped></style>
