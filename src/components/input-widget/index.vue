<template>
  <view class="input-view"
        :class="{'disabled': disabled, 'no-border': noBorder, 'white-bg': !noBgColor}"
        :style="{'height':_itemHeight + 'px','padding': padding}"
  >
    <view class="label"
          :style="{'width': _labelWidth + 'px', 'height': _itemHeight + 'px', 'line-height': _itemHeight + 'px'}"
          v-if="label">
      <text>{{ label }}</text>
      <text class="red" v-if="isRequired">*</text>
    </view>

    <view class="m-input-view" v-if="type==='input'">
      <input
          class="m-input"
          :type="inputType"
          :focus="inputFocus"
          :value="value"
          :disabled="disabled"
          :password="password"
          :hold-keyboard="holdKeyboard"
          @input="onKeyInput"
          @confirm="inputConfirm"
          @focus="focus"
          @blur="blur"
          placeholder-class="input-placeholder"
          :placeholder="placeholder?placeholder: '请输入' + label"
          :style="{'height':upxTopx(itemHeight) + 'px', 'line-height':_itemHeight + 'px','font-size': fontSize + 'px'}"
      />
      <text class="iconfont icon-clear" v-if="value&&showClear&&isFocusing" @click="clearValue"></text>
    </view>

    <view class="m-input-view" v-if="type==='textarea'">
      <u-textarea
          :value="value"
          placeholderClass="input-placeholder"
          :placeholder="placeholder?placeholder: '请输入' + label"
          :disabled="disabled"></u-textarea>
    </view>
    <view
        v-if="type==='select'"
        class="m-select"
        :style="{'height':_itemHeight + 'px'}"
        @click="select">
      <view class="value" :class="{'no-value': !value}">
        {{ value ? value : (placeholder ? placeholder : '请选择' + label) }}
      </view>
      <view class="icon-view">
        <text class="iconfont icon-you1"></text>
      </view>
    </view>
    <view class="m-switch" v-if="type==='switch'"
          :style="{'height':_itemHeight + 'px', 'line-height':_itemHeight + 'px'}">
      <switch :checked="value" @change="switchChange"/>
    </view>
    <slot name="btn"></slot>
  </view>
</template>

<script lang="ts">
import {Component, Emit, Prop, Vue} from 'vue-property-decorator'
import {Modal} from "@/utils"
import {InputTypeDto} from '@/utils/types'

@Component({
  name: 'inputWidget'
})
export default class inputWidget extends Vue {
  @Prop() isRequired: boolean = false
  @Prop({default: ''}) label: string = ''
  @Prop() inputType: string = 'text'
  @Prop({required: true}) value!: string
  @Prop() placeholder: string = ''
  @Prop() disabled: boolean = false
  @Prop() password: boolean = false
  @Prop() inputFocus: boolean = false
  @Prop() noBorder: boolean = false
  @Prop() noBgColor: boolean = false
  @Prop() holdKeyboard: boolean = false
  @Prop({default: InputTypeDto.INPUT}) type!: string
  @Prop({default: true}) showClear!: boolean
  @Prop({default: 100}) itemHeight!: number
  @Prop({default: 140}) labelWidth!: number
  @Prop({default: 15}) fontSize!: number
  @Prop({default: '0 20upx'}) padding!: string
  private isFocusing: boolean = false

  get _labelWidth() {
    return this.upxTopx(this.labelWidth)
  }

  get _itemHeight() {
    return this.upxTopx(this.itemHeight)
  }

  @Emit('input')
  inputEvent(value: string) {
    return value
  }

  @Emit('inputConfirm')
  inputConfirmEvent(value: string) {
    return value
  }

  @Emit('select')
  selectEvent(value: string) {
    return value
  }

  @Emit('change')
  changeEvent(value: boolean) {
    return value
  }

  upxTopx(value: number) {
    return uni.upx2px(value)
  }

  focus() {
    this.isFocusing = true
  }

  blur() {
    this.isFocusing = false
  }

  onKeyInput(e: any) {
    const val: string = e.detail.value
    this.inputEvent(val)
  }

  clearValue() {
    this.inputEvent('')
  }

  inputConfirm() {
    this.inputConfirmEvent(this.value)
  }

  switchChange(e: any) {
    const val: boolean = e.detail.value
    this.changeEvent(val)
  }

  select() {
    if (this.disabled) {
      Modal.toast('当前不可操作哦~')
      return false
    }
    this.selectEvent(this.value)
  }
}

</script>

<style lang="scss" scoped>
$inputHeight: 90 upx;
.disabled {
  color: #9b9b9b !important;
}

.no-border {
  border: none !important;
}

.white-bg {
  background-color: #ffffff;
}

.input-view {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  border-bottom: 1px solid #eeeeee;
  padding: 0 20 upx;
  box-sizing: border-box;
  width: 100%;

  .label {
    text-align: left;
    font-size: $uni-font-size-sm;
    //color: #242424;
    margin-right: 10 upx;
    flex-shrink: 0;
  }

  .red {
    color: red;
  }

  .m-input-view {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
      ::v-deep textarea {
        font-size: 26upx !important;
        line-height: 30upx;
        height:90upx !important;
      }
    .m-input {
      flex: 1;
      background-color: transparent;
      //max-width: 500upx;
      font-size: $uni-font-size-base;
    }

    ::v-deep .input-placeholder {
      color: #9e9e9e;
      font-size: $uni-font-size-base;
    }

    .icon-clear {
      font-size: 42 upx;
      margin: 0 20 upx;
      color: #9E9E9E;
    }
  }

  .m-select {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: $uni-font-size-base;

    .value {
      flex: 1;
      //line-height: $inputHeight;
      text-overflow: ellipsis;
      overflow: hidden;
      color: #000000;
      white-space: nowrap;
      max-width: 450 upx;
    }

    .no-value {
      color: #999999;
    }

    .icon-view {
      width: 60 upx;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      .icon-you1 {
        margin: 0 20 upx;
        color: #9e9e9e;
        font-size: 42 upx;
      }
    }
  }

  .m-switch {
    flex: 1;

    switch {
      transform: scale(.8);
    }
  }
}
</style>
