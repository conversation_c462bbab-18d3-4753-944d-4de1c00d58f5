<template>
  <view class="full-width flex-row">
    <view class="clear-search">
      <view class="search" @click="search">
        <text class="iconfont icon-search"></text>
      </view>
    </view>
    <input
        class="m-input"
        :value="value"
        :disabled="disabled"
        @input="onKeyInput"
        @confirm="inputConfirm"
        :placeholder="placeholder"
    />
    <view class="scan-view" v-if="showScan && value === ''" @click="scan">
      <text class="iconfont icon-saoma"></text>
    </view>

    <view class="clear-search" v-if="value !== ''">
      <view class="clear" @click="clear">
        <text class="iconfont icon-closeTwo"></text>
      </view>
      <!--            <view class="border"></view>-->
      <!--            <view class="search" @click="search">-->
      <!--                <text class="iconfont iconsearch"></text>-->
      <!--            </view>-->
    </view>
  </view>
</template>

<script lang="ts">
import {Component, Vue, Prop, Emit} from 'vue-property-decorator'

@Component({
  name: 'searchInput'
})
export default class SearchInput extends Vue {
  @Prop({required: true, default: ''}) value!: string
  // @Prop() name: string | undefined
  @Prop({default: '请输入'}) placeholder!: string // 默认值
  @Prop({type: Boolean, default: true}) showScan!: boolean // 显示扫码图标
  @Prop({type: Boolean, default: false}) disabled!: boolean // 是否可以编辑

  private scanResult: any = ''


  @Emit('input')
  inputEvent(value: string) {
    return value
  }

  @Emit('search')
  searchEvent() {
    return this.value
  }

  @Emit('scan')
  scanEvent() {
    return this.scanResult
  }

  @Emit('inputConfirm')
  inputConfirmEvent(value: string) {
    return value
  }

  // 输入方法
  onKeyInput(e: any) {
    const val = e.detail.value
    this.inputEvent(val)
  }

  inputConfirm() {
    this.inputConfirmEvent(this.value)
  }

  // 扫码方法
  scan() {
    uni.scanCode({
      success: res => {
        this.scanResult = res.result
        this.scanEvent()
      },
      fail: err => {
        // Modal.alert('扫码失败')
      }
    })
  }

  // 清空
  clear() {
    this.inputEvent('')
    this.inputConfirm()
  }

  // 查询方法
  search() {
    this.searchEvent()
  }

  // 生命周期
  mounted() {
  }
}

</script>

<style lang="scss" scoped>
.full-width {
  padding-left: 20upx;
  height: 70upx;
  background-color: #F7F7F7;
  margin: 20upx;
  border-radius: 45upx;
}
.m-input {
  flex: 1;
  //max-width: 500upx;
  font-size: $uni-font-size-sm;
}

input {
  height: 100%;
  width: 500upx;
  justify-content: flex-start;
  align-items: center;
}

.scan-view {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20upx;

  .iconfont {
    font-size: 45upx;
    font-weight: normal;
    color: black;
  }
}

.clear-search {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

  .clear {
    padding: 0 20upx;
    color: #b7b7b7;

    .iconfont {
      font-size: 35upx;
      line-height: 90upx;
    }
  }

  .border {
    width: 2upx;
    height: 70%;
    background-color: #dcdcdc;
  }

  .search {
    padding: 0 20upx;
    color: black;

    .iconfont {
      font-size: 45upx;
      line-height: 90upx;
    }
  }
}
</style>
