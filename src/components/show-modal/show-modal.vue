<template>
    <view v-show="show" class="_showModal">
        <view class="_shade"></view>
        <view class="_modalBox">
            <view class="_modal">
                <slot name="title">
                    <view v-show="title" class="title">{{ title }}</view>
                </slot>
                <slot name="content">
                    <view class="content">
                        {{ content }}
                        <slot name="popup"></slot>
                    </view>
                </slot>
                <slot name="btn">
                    <view class="btnBox">
                        <view v-if="showCancel" :style="{color:cancelColor,background:cancelBackgroundColor}" class="btn"
                              @click.stop="clickBtn('cancel')">{{ cancelText }}
                        </view>
                        <view v-if="showCancel" class="border"></view>
                        <view :class="showCancel? 'btn': 'btn2'" :style="{color:confirmColor,background:confirmBackgroundColor}"
                              @click.stop="clickBtn('confirm')">{{ confirmText }}
                        </view>
                    </view>
                </slot>
            </view>
        </view>
        <view class="_advertisement">
            <ad v-if="BrandName === '熊猫智能柜'" unit-id="adunit-e0819335bf635057"></ad>
            <ad v-if="BrandName === '便利智能柜'" unit-id="adunit-5adceb6dc849d2ae"></ad>
        </view>
    </view>
</template>

<script>
import config from "@/utils/config";

export default {
    name: "show-modal",
    computed: {
        show() {
            return this.$modalStore.state.show;
        },
        title() {
            return this.$modalStore.state.title;
        },
        content() {
            return this.$modalStore.state.content;
        },
        showCancel() {
            return this.$modalStore.state.showCancel;
        },
        cancelText() {
            return this.$modalStore.state.cancelText;
        },
        cancelColor() {
            return this.$modalStore.state.cancelColor;
        },
        cancelBackgroundColor() {
            return this.$modalStore.state.cancelBackgroundColor;
        },
        confirmText() {
            return this.$modalStore.state.confirmText;
        },
        confirmColor() {
            return this.$modalStore.state.confirmColor;
        },
        confirmBackgroundColor() {
            return this.$modalStore.state.confirmBackgroundColor;
        }
    },
    methods: {
        closeModal() {
            this.$modalStore.commit('hideModal')
        },
        clickBtn(res) {
            this.$modalStore.commit('hideModal')
            this.$modalStore.commit('success', res)
        }
    },
    beforeDestroy() {
        this.$modalStore.commit('hideModal')
    },
    data() {
        return {
            BrandName: config.BrandName
        };
    }
}
</script>

<style lang="scss" scoped>
._showModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;

  ._shade {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: #000;
    opacity: .6;
    z-index: 11000;
  }

  ._advertisement {
    width: 100%;
    position: fixed;
    bottom: 0;
    z-index: 12000;
  }

  ._modalBox {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 12000;
    display: flex;
    justify-content: center;
    align-items: center;

    ._modal {
      flex: none;
      width: 80%;
      background: #fff;
      border-radius: 16upx;
      overflow: hidden;

      .title {
        text-align: center;
        font-size: 38upx;
        font-weight: bold;
        color: #333333;
        margin-top: 50upx;
      }

      .content {
        min-height: 140upx;
        width: 90%;
        margin: 20upx auto;
        font-size: 34upx;
        color: #7F7F7F;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
      }

      .btnBox {
        display: flex;
        margin: auto;
        flex-direction: row;
        justify-content: space-between;
        border-top: 2upx solid #EAEAEA;

        .border{
          width: 2upx;
          background-color: #EAEAEA;
        }
        .btn2{
          width: 100%;
          height: 105upx;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 34upx;
        }

        .btn {
          width: 49%;
          height: 105upx;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 34upx;
        }
      }
    }
  }

}
</style>
