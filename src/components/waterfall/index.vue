<template>
  <view class="fall-container">
    <u-waterfall v-model="flowList" ref="uWaterfall">
      <template v-slot:left="{ leftList }">
        <left-item v-for="(item, index) in leftList" :key="index" :item="item"></left-item>
      </template>
      <template v-slot:right="{ rightList }">
        <left-item v-for="(item, index) in rightList" :key="index" :item="item"></left-item>
      </template>
    </u-waterfall>
    <u-loadmore bg-color="#f6f6f7" :status="loadStatus" @loadmore="addRandomData"></u-loadmore>
  </view>
</template>

<script lang="ts">
import {Component, Vue, Prop, Emit, Watch} from 'vue-property-decorator'
import Config from '@/utils/config'
import {Utils, Cache} from '@/utils/index'
import {waybillDto} from '@/utils/types'
import LeftItem from "@/components/waterfall/leftItem.vue"

@Component({
  name: 'waterfall',
  components: {
    LeftItem
  }
})
export default class Waterfall extends Vue {
  $refs!: {
    uWaterfall: HTMLFormElement
  }

  private loadStatus: string = 'loadmore'
  private flowList: any[] = []
  private list: any[] = [
    {
      price: 35,
      title: '北国风光，千里冰封，万里雪飘',
      shop: '李白杜甫白居易旗舰店',
      image: 'http://pic.sc.chinaz.com/Files/pic/pic9/202002/zzpic23327_s.jpg'
    },
    {
      price: 75,
      title: '望长城内外，惟余莽莽',
      shop: '李白杜甫白居易旗舰店',
      image: 'http://pic.sc.chinaz.com/Files/pic/pic9/202002/zzpic23325_s.jpg'
    },
    {
      price: 385,
      title: '大河上下，顿失滔滔',
      shop: '李白杜甫白居易旗舰店',
      image: 'http://pic2.sc.chinaz.com/Files/pic/pic9/202002/hpic2119_s.jpg'
    },
    {
      price: 784,
      title: '欲与天公试比高',
      shop: '李白杜甫白居易旗舰店',
      image: 'http://pic2.sc.chinaz.com/Files/pic/pic9/202002/zzpic23369_s.jpg'
    },
    {
      price: 7891,
      title: '须晴日，看红装素裹，分外妖娆',
      shop: '李白杜甫白居易旗舰店',
      image: 'http://pic2.sc.chinaz.com/Files/pic/pic9/202002/hpic2130_s.jpg'
    },
    {
      price: 2341,
      shop: '李白杜甫白居易旗舰店',
      title: '江山如此多娇，引无数英雄竞折腰',
      image: 'http://pic1.sc.chinaz.com/Files/pic/pic9/202002/zzpic23346_s.jpg'
    },
    {
      price: 661,
      shop: '李白杜甫白居易旗舰店',
      title: '惜秦皇汉武，略输文采',
      image: 'http://pic1.sc.chinaz.com/Files/pic/pic9/202002/zzpic23344_s.jpg'
    },
    {
      price: 1654,
      title: '唐宗宋祖，稍逊风骚',
      shop: '李白杜甫白居易旗舰店',
      image: 'http://pic1.sc.chinaz.com/Files/pic/pic9/202002/zzpic23343_s.jpg'
    },
    {
      price: 1678,
      title: '一代天骄，成吉思汗',
      shop: '李白杜甫白居易旗舰店',
      image: 'http://pic1.sc.chinaz.com/Files/pic/pic9/202002/zzpic23343_s.jpg'
    },
    {
      price: 924,
      title: '只识弯弓射大雕',
      shop: '李白杜甫白居易旗舰店',
      image: 'http://pic1.sc.chinaz.com/Files/pic/pic9/202002/zzpic23343_s.jpg'
    },
    {
      price: 8243,
      title: '俱往矣，数风流人物，还看今朝',
      shop: '李白杜甫白居易旗舰店',
      image: 'http://pic1.sc.chinaz.com/Files/pic/pic9/202002/zzpic23343_s.jpg'
    }
  ]
  private id:number = 0

  addRandomData() {
    for (let i = 0; i < 10; i++) {
      let index: number = Math.floor(Math.random()*10)
      // 先转成字符串再转成对象，避免数组对象引用导致数据混乱
      let item = JSON.parse(JSON.stringify(this.list[index]))
      item.id = this.id++
      this.flowList.push(item)
    }
  }

  remove(id: string) {
    // this.$refs.uWaterfall.remove(id)
  }
  clear() {
    this.$refs.uWaterfall.clear()
  }

  mounted() {
    this.addRandomData()
  }
}

</script>

<style lang="scss" scoped>
.demo-warter {
  border-radius: 8px;
  margin: 5px;
  background-color: #ffffff;
  padding: 8px;
  position: relative;
  border: 1px solid #000000;
}

.u-close {
  position: absolute;
  top: 32upx;
  right: 32upx;
}

.demo-img-wrap {
}

.demo-image {
  width: 100%;
  border-radius: 4px;
}

.demo-title {
  font-size: 30upx;
  margin-top: 5px;
  color: #303133;
}

.demo-tag {
  display: flex;
  margin-top: 5px;
}

.demo-tag-owner {
  background-color: #fa3534;
  color: #ffffff;
  display: flex;
  align-items: center;
  padding: 4upx 14upx;
  border-radius: 50upx;
  font-size: 20upx;
  line-height: 1;
}

.demo-tag-text {
  border: 1px solid #2979ff;
  color: #2979ff;
  margin-left: 10px;
  border-radius: 50upx;
  line-height: 1;
  padding: 4upx 14upx;
  display: flex;
  align-items: center;
  font-size: 20upx;
}

.demo-price {
  font-size: 30upx;
  color: #fa3534;
  margin-top: 5px;
}

.demo-shop {
  font-size: 22upx;
  color: #909399;
  margin-top: 5px;
}
</style>
