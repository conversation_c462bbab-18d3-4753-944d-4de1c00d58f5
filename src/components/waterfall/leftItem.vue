<template>
  <view class="left-item">
    <view class="demo-warter">
      <u-lazy-load threshold="-450" border-radius="10" :image="item.image" :index="index"></u-lazy-load>
      <view class="demo-title">{{ item.title }}</view>
      <view class="demo-price">{{ item.price }}元</view>
      <view class="demo-tag">
        <view class="demo-tag-owner">自营</view>
        <view class="demo-tag-text">放心购</view>
      </view>
      <view class="demo-shop">{{ item.shop }}</view>
    </view>
  </view>
</template>

<script lang="ts">
import {Component, Vue, Prop} from 'vue-property-decorator'
import Config from '@/utils/config'
import {Utils, Cache} from '@/utils/index'

@Component({
  name: 'leftItem'
})
export default class LeftItem extends Vue {
  @Prop({
    required: true,
    default: () => {
      return {}
    }
  }) item!: any
}

</script>

<style lang="scss" scoped>
.left-item{
  //margin-left: 10upx;
}
.demo-warter {
  border-radius: 8px;
  margin: 5px;
  background-color: #ffffff;
  padding: 8px;
  position: relative;
}

.u-close {
  position: absolute;
  top: 32upx;
  right: 32upx;
}

.demo-img-wrap {
}

.demo-image {
  width: 100%;
  border-radius: 4px;
}

.demo-title {
  font-size: 30upx;
  margin-top: 5px;
  color: #303133;
}

.demo-tag {
  display: flex;
  margin-top: 5px;
}

.demo-tag-owner {
  background-color: #fa3534;
  color: #ffffff;
  display: flex;
  align-items: center;
  padding: 4upx 14upx;
  border-radius: 50upx;
  font-size: 20upx;
  line-height: 1;
}

.demo-tag-text {
  border: 1px solid #2979ff;
  color: #2979ff;
  margin-left: 10px;
  border-radius: 50upx;
  line-height: 1;
  padding: 4upx 14upx;
  display: flex;
  align-items: center;
  font-size: 20upx;
}

.demo-price {
  font-size: 30upx;
  color: #fa3534;
  margin-top: 5px;
}

.demo-shop {
  font-size: 22upx;
  color: #909399;
  margin-top: 5px;
}
</style>
