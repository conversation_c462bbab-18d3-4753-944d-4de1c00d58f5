<template>
  <view class="waybill">
    <!--待取件-->
    <view class="DQJ" @click="toDetail" v-if="!isSign">
      <view class="flex-space">
        <view class="flex-row title-view">
          <image :src="companyLogo" class="logo"></image>
          <text class="title">{{ waybill.brandName }}</text>
          <text class="title-waybillNo">{{ waybill.waybillNo }}</text>
        </view>

        <view class="date">{{ getInboundDay }}</view>
      </view>
      <view class="content-view">
        <view class="code-view">
          <text>取件码:</text>
          <text class="code">{{ waybill.checkCode }}</text>
        </view>
        <view class="desc-view">
          <text class="desc"></text>
        </view>
      </view>
    </view>

    <!--已取件-->
    <view class="YQJ" @click="toDetail" v-if="isSign">
      <view class="left">
        <image :src="companyLogo"></image>
      </view>
      <view class="right">
        <view class="status">已签收</view>
        <view class="font-normal">{{ companyText }} | {{ waybill.waybillNo }}</view>
        <view class="font-normal">签收时间: {{ waybill.signDate }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
import {Component, Vue, Prop, Emit, Watch} from 'vue-property-decorator'
import Config from '@/utils/config'
import {Utils, Cache} from '@/utils/index'
import {waybillDto} from '@/utils/types'

@Component({
  name: 'waybill'
})
export default class Waybill extends Vue {
  @Prop({
    required: true,
    default: () => {
      return {}
    }
  }) waybill!: waybillDto
  @Prop({
    required: true
  }) type!: string
  // 是否是已签收
  @Prop({
    default: false
  }) isSign!: boolean

  // 计算属性
  get companyLogo() {
    return Utils.getCompanyLogo(this.waybill.brandCode)
  }

  get companyText() {
    return Config.EXPRESS[this.waybill.brandCode]
  }

  get getInboundDay() {
    if (this.waybill.inboundYmd) {
      return this.waybill.inboundYmd.substring(5, 10)
    }
    return ''
  }

  toDetail() {
    Cache.setItem('waybillDetail', this.waybill)
    uni.navigateTo({
      url: '/pages/main/waybillDetail?isSign=' + this.isSign
    })
  }
}

</script>

<style lang="scss" scoped>
.waybill {
  padding: 30upx 0;
  background-color: #ffffff;
  border-bottom: 1px dashed #f1f1f1;
}

.DQJ {
  .title-view {
    align-items: center;
  }

  .logo {
    width: 50upx;
    height: 50upx;
    border-radius: 10upx;
  }

  .title {
    margin-left: 15upx;
    font-size: 30upx;
    color: #070707;
  }

  .title-waybillNo {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-left: 15upx;
    font-size: 32upx;
    width: 310upx;
  }

  .date {
    text-align: right;
    font-size: 28upx;
    color: grey;
    width: 100upx;
  }

  .content-view {
    margin-left: 70upx;
    margin-top: 10upx;
  }

  .code-view {
    justify-content: flex-start;
    align-items: flex-end;

    text {
      font-size: 28upx;
      color: grey;
      margin-right: 20upx;
    }

    .code {
      font-size: 50upx;
      color: #1b1b1b;
      font-weight: bolder;
    }
  }

  .desc-view {
    margin-top: 10upx;
    width: 600upx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    .desc {
      font-size: 28upx;
      color: grey;
    }
  }
}

.YQJ {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;

  .left {
    margin-top: 5upx;

    image {
      width: 50upx;
      height: 50upx;
      border-radius: 10upx;
    }
  }

  .right {
    flex: 1;
    margin-left: 20upx;

    .status {
      font-size: 36upx;
      font-weight: 600;
      color: $uni-text-color-default;
    }

    .font-normal {
      font-size: $uni-font-size-sm;
      color: $uni-text-color-grey
    }
  }
}
</style>
