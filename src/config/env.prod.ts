import {ConfigInterface} from './index';
export const prod: ConfigInterface = {
    // 请求地址
    baseApi: "https://api.711bear.com",    
    // 请求key
    appKey: "xmzng", 
    // 请求秘钥
    secret: "Xmzngqwe123",//Xmzngqwe123
    // oss地址
    ossApi: "https://cabinet-img.zlxx365.com",
    // 协议域名
    agreementDomain: "https://s.711bear.com",
   // 使用类型断言绕过 TypeScript 检查
    // @ts-ignore: 条件编译会在构建时处理
    appid: 'wx8dff26368aa613e0', // 默认值
    // 微信小程序配置
    // #ifdef MP-WEIXIN
    // @ts-ignore: 条件编译会在构建时处理
    appid: 'wx8dff26368aa613e0',
    // #endif
    // 支付宝小程序配置
    // #ifdef MP-ALIPAY
    // @ts-ignore: 条件编译会在构建时处理
    appid: '2021005152629113',
    // #endif
    // 主色调
    color: "#FF5E24",
    // 品牌名称
    brandName: "熊猫智能柜",
};
