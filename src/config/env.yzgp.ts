import {ConfigInterface} from './index';

export const yzgp: ConfigInterface = {
    // 请求地址
    baseApi: "https://blzng-api.711bear.com",
    // 请求key
    appKey: "blzng",
    // 请求秘钥
    secret: "Blzngqwe123",
    // oss地址
    ossApi: "https://blzng-img.711bear.com",
    // 协议域名
    agreementDomain: "https://blzng-s.711bear.com",
       // 使用类型断言绕过 TypeScript 检查
    // @ts-ignore: 条件编译会在构建时处理
    appid: 'wx6302929c09fe55cf', // 默认值
    // 微信小程序配置
    // #ifdef MP-WEIXIN
    // @ts-ignore: 条件编译会在构建时处理
    appid: 'wx6302929c09fe55cf',
    // #endif
    // 支付宝小程序配置
    // #ifdef MP-ALIPAY
    // @ts-ignore: 条件编译会在构建时处理
    appid: '2021005152629113',
    // #endif
    // 主色调
    color: "#1871ee",
    // 品牌名称
    brandName: "便利智能柜",
};
