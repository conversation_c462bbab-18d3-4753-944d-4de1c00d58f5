import {def} from './env.def';
import {prod} from './env.prod';
import {yzgp} from "./env.yzgp";
import {test} from "./env.test";



export interface ConfigInterface {
    // 请求地址
    baseApi: string;
    // 请求key
    appKey: string;
    // 请求秘钥
    secret: string;
    // oss地址
    ossApi: string;
    // 协议域名
    agreementDomain: string;
    // 微信小程序appid
    appid: string;
    // 主色调
    color: string;
    // 品牌名称
    brandName: string;
}

let Config: ConfigInterface;
console.log(process.env.VUE_APP_CHANNEL)
switch (process.env.NODE_ENV) {
    case 'production':
        if (process.env.VUE_APP_CHANNEL === 'yzgp') {
            Config = yzgp;
        } else {
            Config = prod;
        }
        break;
    default:
        if (process.env.VUE_APP_CHANNEL === 'test') {
            Config = test;
        } else {
            Config = def;
        }
        break;
}
export default Config;
