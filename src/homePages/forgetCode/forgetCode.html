<view class="forgetCode">
    <u-navbar :bgColor="backgroundColor" :catchtouchmove="true" fixed leftIconColor='#FFFFFF'
              placeholder title="忘记取件码" titleStyle="color:#FFFFFF" @left-click="backHome">
    </u-navbar>
    <view class="container">
        <view class="container-code padding030">
            <view class="input">
                <input-widget v-model="valArr.val" :fontSize="22" :item-height="80" :noBorder="true" :show-clear="false"
                              class="input-style"
                              placeholder="请输入快递单号/单号后6位" @input="codeNum"
                              @inputConfirm="getCheckCode"
                >
                </input-widget>
            </view>
        </view>
        <scroll-view class="scroll-view" lower-threshold="200" scroll-y>
            <view class="scroll-view-list">
                <view v-if="packageList.length>0">
                    <view v-for="(item, index) in packageList" :key="index">
                        <view>
                            <view class="name">{{ item.cabinetLocationName }}</view>
                            <view class="item">
                                <view class="item-left">
                                    <view :class="index>2? 'item-left-box fz54':'item-left-box fz80'">
                                        {{ item.cabinetBoxLabel }}
                                    </view>
                                    <view>号格口</view>
                                </view>
                                <view class="item-right">
                                    <view class="item-checkCode">
                                        <view class="flex-start">
                                            <view v-if="item.secretWaybill === 1" class="label">隐私</view>
                                            <view class="checkCode">{{ item.checkCode }}</view>
                                            <view v-if="item.cabinetType===3" class="label ml20">货架</view>
                                        </view>
                                        <view class="item-btn" @click="copy(item.checkCode)">复制</view>
                                    </view>
                                    <view v-if="item.secretWaybill === 1">
                                        手机尾号: {{ item.receiverMobileLast4 }}
                                    </view>
                                    <view class="item-price">
                                        超时费：
                                        <view v-if="item.price > 0">
                                            <span class="fee-un-pay">{{ (item.price / 1000).toFixed(2) }}</span>元
                                        </view>
                                        <view v-else>暂无</view>
                                    </view>
                                    <view class="item-detail">
                                        <view v-if="item.orderType!==2" class="mr10">
                                            {{ item.brandName }}:**{{ item.waybillNo.slice(-4) }}
                                        </view>
                                        <view>
                                            {{ item.inboundTime }}
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-30"></view>
                </view>
                <view class="illustrate">
                    您还可以通过如下方式查询取件码:
                    <view class="ml20">
                        1.淘宝件可通过支付宝菜鸟小程序查看所有取件码
                    </view>
                    <view class="ml20">
                        2.拼多多、抖音、京东件可通过商品详情页物流信息查看
                    </view>
                </view>
            </view>
        </scroll-view>
        <view class="advertisement">
            <ad v-if="BrandName === '熊猫智能柜'" unit-id="adunit-62ea480d804babe4"></ad>
            <ad v-if="BrandName === '便利智能柜'" unit-id="adunit-8a2d5c8868f6c4b2"></ad>
        </view>
    </view>
</view>