<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import SearchInput from "@/components/search-input/index.vue"
import {ImageMixins} from "@/mixins/ImageMixins";
import InputWidget from "@/components/input-widget/index.vue";
import config from "@/utils/config";
import {getCheckCode} from "@/model/cabinet";


@Component({
    name: 'nearCabinet',
    components: {
        InputWidget,
        SearchInput
    }
})
export default class NearCabinet extends ImageMixins {
    private BrandName = config.BrandName
    private packageList: any = []
    private valArr = {val: '', length: 8}
    private backgroundColor = config.CONFIG_COLOR

    // 复制
    copy(data: any) {
        uni.setClipboardData({
            data,
            success: function () {
                uni.getClipboardData({
                    success: function (res) {
                        uni.showToast({
                            title: "复制成功",
                        });
                    }
                });
            }
        });
    }

    async getCheckCode() {
        let info = {
            waybillNo: this.valArr.val
        }
        let res = await getCheckCode(info)
        console.log(res)
        this.packageList = [res]
    }

    // 输入关键字
    codeNum(e: any) {
        this.valArr.val = e
    }

    backHome() {
        uni.switchTab({url: '/pages/home/<USER>/index'})
    }

    onShow() {

    }

    onLoad() {

    }
}
</script>

<template src="./forgetCode.html"></template>
<style lang="scss" scoped src="./forgetCode.scss"></style>
