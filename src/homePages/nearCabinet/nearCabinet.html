<div class="nearCabinet">
    <view class="top">
        <view class="bar">
            <view @click="goChooseLocation">
                <search-input v-model="keyword" placeholder="请输入地址" disabled :show-scan="false"></search-input>
            </view>
            <view class="num-view">
                <view class="item" @click="changeTab('kdg')">
                    <text class="label" :class="{'active':type === 'kdg'}">快递柜</text>
                </view>
                <view class="item" @click="changeTab('yz')">
                    <text class="label" :class="{'active':type === 'yz'}">驿站</text>
                </view>
            </view>
        </view>
    </view>
    <view class="bottom">
        <map class="map"
             id="myMap"
             :latitude="location.latitude"
             :longitude="location.longitude"
             :markers="maskList"
             show-location
             @markertap="bindmarkertap"
             @controltap="controltap"
             @callouttap="clickCallout"></map>
        <view class="location" @click="locationOrGoSetting">
            <text class="iconfont icon-location icon"></text>
            <view class="city">{{ city }}</view>
            <view class="reload">{{ locationAuth ? '重新定位' : '去授权' }}</view>
        </view>
        <view class="cabinet-list">
            <view class="item" v-for="(item, index) in cabinetList" :key="index" @click="moveLocation(index)">
                <view class="space-two">
                    <view>
                        <view class="name">
                            {{ item.name }}
                        </view>
                        <view class="address">
                            {{ item.address + item.address }}
                        </view>
                    </view>
                    <view class="right" @click="openLocation(item)">
                        <view class="distance">
                            {{ getDistance(item.distance) }}
                        </view>
                        <view class="go-there">
                            去这里
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</div>
