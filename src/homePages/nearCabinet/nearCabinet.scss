.nearCabinet {
  position: relative;
  width: 750upx;
  .top{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }
  .bar{
    width: 100%;
    padding-top: 5upx;
    box-sizing: border-box;
    height: 180upx;
    background-color: #ffffff;
  }

  .num-view{
    width: 750upx;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    height: 58upx;
    background-color: #ffffff;
    .item{
      flex: 1;
      color: $uni-color-error;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      .number-bar{
        padding: 1upx 12upx 1upx 12upx;
        display: flex;
        justify-content: center;
        align-content: center;
        background: $config-color;
        border-radius: 50upx;
        margin-bottom: 20upx;
      }
      .label{
        font-size: 33upx;
        color: #333333;
      }
      .active{
        border-bottom: 2px solid $config-color;
      }
    }
  }
  .bottom {
    position: absolute;
    top: 180upx;
    left: 0;
    right: 0;
    bottom: 0;
    .map{
      width: 750upx;
      height: 100%;
    }
    .location{
      position: absolute;
      top: 32upx;
      left: 30upx;
      min-width: 308upx;
      height: 70upx;
      background: #FFFFFF;
      border-radius: 35upx;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: row;
      .icon {
        font-size: 32upx;
        color: $config-color;
      }
      .city{
        font-size: 28upx;
        font-weight: 400;
        color: #333333;
        margin-right: 10upx;
        margin-left: 10upx;
      }
      .reload{
        font-size: 28upx;
        font-weight: 400;
        color: $config-color;
      }
    }
    .cabinet-list {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 30upx;
      background: transparent;
      .item{
        width: 690upx;
        height: 148upx;
        background: #FFFFFF;
        margin-top: 30upx;
        box-sizing: border-box;
        padding: 20upx 30upx;
        border-radius: 10px;
        //.right{
        //  display: flex;
        //  flex-direction: column;
        //  justify-content: flex-end;
        //}
        .space-two {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }
        .name{
          font-size: 32upx;
          font-weight: 400;
          color: #000000;
        }
        .address{
          margin-top: 10upx;
          font-size: 30upx;
          font-weight: 400;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 495upx;
          white-space: nowrap;
          color: #999999;
        }
        .distance{
          font-size: 30upx;
          font-weight: 400;
          color: #999999;
          text-align: end;
        }
        .go-there{
          min-width: 131upx;
          height: 53upx;
          background: $config-color;
          border: 1px solid $config-color;
          border-radius: 27px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 26upx;
          font-weight: 400;
          color: #FFFFFF;
        }
      }
    }
  }
}
