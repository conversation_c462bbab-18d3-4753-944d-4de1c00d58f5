<script lang="ts">
import "reflect-metadata";
import { Component, Prop, Vue } from "vue-property-decorator";
import { isAlipay, authGuideLocation ,getCityCode} from "@/utils/core/uni";
import SearchInput from "@/components/search-input/index.vue";
import { Modal } from "@/utils";
import Config from "@/utils/config";
import { locationDto, longLatDto } from "@/utils/types";
import { getCabinetList } from "@/model/cabinet";
import { ImageMixins } from "@/mixins/ImageMixins";

var QQMapWX = require("@/utils/core/qqmap-wx-jssdk.min.js");
var qqmapsdk = new QQMapWX({
  key: Config.QQMAP_KEY,
});

@Component({
  name: "nearCabinet",
  components: {
    SearchInput,
  },
})
export default class NearCabinet extends ImageMixins {
  private keyword: string = "";
  private type: string = "kdg";
  private location: locationDto = {
    latitude: 0,
    longitude: 0,
  };
  private cabinetList: any = [];
  private maskList: any = [];
  private city: string = "未定位";
  private address: string = "";
  private locationAuth: boolean = false;
  private _mapContext: any;
  private refreshPage: boolean = true;

  locationOrGoSetting() {
    if (this.locationAuth) {
      this.getLocation();
    } else {
      uni.openSetting({});
    }
  }

  changeTab(type: string) {
    if (type == "yz") {
      Modal.toast("敬请期待");
      return;
    }
    this.type = type;
  }

  getDistance(distance: number) {
    let str = "";
    if (distance >= 1000) {
      str = (distance / 1000).toFixed(1) + "km";
    } else {
      str = distance.toFixed(0) + "m";
    }
    return str;
  }

  goChooseLocation() {
    let index: number = this.maskList.findIndex(
      (item: any) => item.id === undefined
    );
    if (~index) {
      this.maskList.splice(index, 1);
    }
    const that = this;
    this.refreshPage = false;
    uni.chooseLocation({
      success: function (res: any) {
        that._mapContext?.moveToLocation({
          latitude: res.latitude,
          longitude: res.longitude,
        });
        that.maskList.push({
          latitude: res.latitude,
          longitude: res.longitude,
        });
      },
      fail: function () {
        that.refreshPage = true;
      },
    });
  }

  bindmarkertap(e: any) {
    const id = e.detail.markerId;
    this.maskList.map((item: any) => {
      (item.width = 28), (item.height = 28);
    });
    let index: number = this.maskList.findIndex((item: any) => item.id === id);
    this._mapContext?.moveToLocation({
      latitude: this.maskList[index].latitude,
      longitude: this.maskList[index].longitude,
    });
    if (~index) {
      this.maskList[index].width = 58;
      this.maskList[index].height = 58;
    }
  }

  controltap(e: any) {}

  clickCallout(e: any) {}

  async getCabinet() {
    let info: longLatDto = {
      latitude: this.location.latitude,
      longitude: this.location.longitude,
    };
    this.cabinetList = [];
    this.maskList = [];
    var res = await getCabinetList(info);
    if (res) {
      const data_ = JSON.parse(JSON.stringify(res));
      if (res.length) {
        this.cabinetList = data_.splice(0, 2);
      } else {
        this.cabinetList = res;
      }
      res.forEach((item: any, index: number) => {
        this.maskList.push({
          ...item,
          id: index + 1,
          title: item.name,
          iconPath: this.getImage("logo"),
          width: 28,
          height: 28,
        });
      });
    }
  }

  moveLocation(index: number) {
    this.maskList.map((item: any) => {
      (item.width = 28), (item.height = 28);
    });
    this._mapContext?.moveToLocation({
      latitude: this.maskList[index].latitude,
      longitude: this.maskList[index].longitude,
    });
    this.maskList[index].width = 58;
    this.maskList[index].height = 58;
  }

  getLocation() {
    var that = this;
    uni.getLocation({
      type: "gcj02",
      success: function (res) {
        that.location = res;
        console.log(res);
        that._mapContext?.moveToLocation({
          latitude: res.latitude,
          longitude: res.longitude,
        });
        that.getCabinet();
        qqmapsdk.reverseGeocoder({
          location: {
            latitude: res.latitude,
            longitude: res.longitude,
          },
          success: function (res_: any) {
            let province = res_.result.ad_info.province;
            let city = res_.result.ad_info.city;
            that.city = city;
            that.address = res_.result.address;
          },
          fail: function (err: any) {},
          complete: function (res: any) {},
        });
      },
      fail: function (err) {},
    });
  }

  openLocation(item: any) {
    uni.openLocation({
      longitude: item.longitude,
      latitude: item.latitude,
    });
  }

  // getUserSetting() {
  //   var that = this
  //   uni.getSetting({
  //     success: function (res) {
  //       that.locationAuth = res.authSetting['scope.userLocation']
  //     }
  //   })
  // }
  onShow() {
    var that = this;
    // this.getUserSetting()
    if (this.refreshPage) {
      if (isAlipay()) {
        authGuideLocation().then((res: any) => {
          if (res) {
            that.locationAuth = true;
            that.getLocation();
          } else {
            that.locationAuth = false;
          }
        });
      } else {
        uni.authorize({
          //支付宝小程序不支持uni.authorize
          // @ts-ignore
          scope: "scope.userLocation",
          success() {
            that.locationAuth = true;
            that.getLocation();
          },
          fail() {
            that.locationAuth = false;
          },
        });
      }
    }
  }

  onReady() {
    this._mapContext = uni.createMapContext("myMap", this);
    this._mapContext?.initMarkerCluster({
      enableDefaultStyle: false,
      zoomOnClick: true,
      gridSize: 60,
      complete: function (res: any) {},
    });
    if (!isAlipay()) {
      // 微信小程序环境
      this._mapContext.on && this._mapContext.on("markerClusterCreate", (e: any) => {});
    } 
  }

  onLoad() {}
}
</script>

<template src="./nearCabinet.html"></template>
<style src="./nearCabinet.scss" lang="scss" scoped></style>
