.allPick {
  width: 750upx;
  position: relative;

  .item {
    margin-top: 30upx;
    margin-left: 30upx;
    margin-right: 30upx;
    width: 690upx;
    padding: 30upx;
    box-sizing: border-box;
    background: #FFFFFF;
    border-radius: 10upx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .icon {
      font-size: 92upx;
      color: $config-color;
    }
  }
}
.btn {
  width: 595upx;
  height: 76upx;
  line-height: 76upx;
  box-sizing: border-box;
  border-radius: 45upx;
  text-align: center;
  font-size: $uni-font-size-sm;
}

.title {
  font-size: 30upx;
  font-weight: 400;
  color: #000000;
}
.title2 {
  font-size: 26upx;
  font-weight: 400;
  color: $config-color;
  height: 64upx;
  line-height: 64upx;
}

.auth {
  color: #ffffff;
  background-color: $config-color;
  margin-bottom: 40upx;
  margin-top: 88upx;
}