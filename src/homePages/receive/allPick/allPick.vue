<script lang="ts">
import 'reflect-metadata';
import {Component, Prop, Vue} from 'vue-property-decorator';
import config from "@/utils/config";

@Component({
  name: 'allPick'
})
export default class AllPick extends Vue {
  onLoad(){
    uni.setNavigationBarTitle({
      title:config.BrandName
    })
  }

  closeBox() {
    // uni.reLaunch({
    //   url: '/pages/home/<USER>/index'
    // })
    uni.navigateBack({
      delta: 2
    })
  }
}
</script>

<template src="./allPick.html"></template>
<style src="./allPick.scss" lang="scss" scoped></style>
