<script lang="ts">
import 'reflect-metadata';
import {Component, Vue} from 'vue-property-decorator';
import config from "@/utils/config";
import {openBoxByOrder} from "@/model/send";
import {Cache} from "@/utils";

@Component({
    name: 'allPick'
})
export default class AllPick extends Vue {
    private info: any = {}

    onLoad(e: any) {
        if (e.info) {
            this.info = JSON.parse(decodeURIComponent(e.info))
        }
        uni.setNavigationBarTitle({
            title: config.BrandName
        })
    }

    async openBox() {
        let params = {
            cabinetLocationCode: this.info.cabinetLocationCode,
            checkCode: this.info.checkCode,
            mobile: this.info.mobile,
            hostIndex:Cache.getItem('hostIndex')
        }
        const res = await openBoxByOrder(params)
        console.log(res, '88888')
    }
}
</script>

<template src="./noPick.html"></template>
<style lang="scss" scoped src="./noPick.scss"></style>
