<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import {cabinetCodeDto, payDto, waybillInfo} from "@/utils/types";
import Modal from "@/utils/core/modal";
import {getCabinetInfo, getOrderList, getOrderListInShop, orderByCode, outboundOrder} from "@/model/cabinet";
import {getOrderIsPay, getPayParams} from "@/model/pay";
import paymentService from "@/utils/services/paymentService";
import {ImageMixins} from "@/mixins/ImageMixins";
import {Cache, Utils} from "@/utils";
import config from "@/utils/config";
import InputWidget from "@/components/input-widget/index.vue";

@Component({
  name: 'packageList',
  components: {InputWidget}
})
export default class PackageList extends ImageMixins {
  private bgColor = config.CONFIG_COLOR + '55'
  private BrandName = config.BrandName
  // private cabinetLocationCode: string = ''
  private cabinetLocationCode: string = 'G23032922805'
  private cabinetLocationId: string = ''
  private packageList: waybillInfo[] = []
  private current: number = 1
  private total: number = 0
  private loadStatus: string = 'noMore'
  private valArr = {val: '', length: 8}
  private isFocus = false
  private isShow = false
  private locationName = config.BrandName
  private show = false
  private inputValue = ''
  private title = ''
  private item: any = ''
  private backgroundColor = config.CONFIG_COLOR

  // 获取品牌logo
  getCompanyLogo(code: string) {
    return Utils.getCompanyLogo(code)
  }

  // 输入取件码
  codeNum(e: any) {
    // this.valArr.val = e.detail.value
    this.valArr.val = e
    if (this.valArr.val.length >= this.valArr.length) {
      // this.blur()
      this.getOrderByCode()
    }
  }

  // 失去焦点
  blur() {
    this.isFocus = false
  }

  async getLocationInfo() {
    let res = await getCabinetInfo({code: this.cabinetLocationCode})
    this.locationName = res.name
    if (res.config.hiddenAdvertisement) {
      this.isShow = Number(res.config.hiddenAdvertisement) === 1
    }
    if (res.sixCheckCodeRule === 1) {
      this.valArr.length = 6
    }
  }

  //打开键盘
  KeyBoarOpen(e: any) {
    this.isFocus = true
  }

  // 判断是否隐私件
  async isPick(item: any) {
    if (item.isCurrentCabinetLocation === 0) {
      let msg = '请到' + item.cabinetLocationName + '扫码取件'
      await Modal.confirmCustom(msg, '温馨提示', '取消', '确认', false)
    } else if (item.secretWaybill === 1) {
      this.show = true
      await Modal.confirmCustom('', '温馨提示', '取消', '确认', false)
      this.show = false
    } else {
      await this.pick(item, '')
    }
  }

  // 取件是否正确
  async testCheckCode() {
    this.close()
    // let params = {
    //     orderId: this.item.id,
    //     checkCode: this.inputValue
    // }
    // let res = await testCheckCode(params)
    // if (res) {
    //     this.close()
    //     await this.pick(this.item)
    // } else {
    //     modal.toast('取件码错误')
    // }
  }

  // 关闭弹窗
  close() {
    this.show = false
    this.inputValue = ''
  }


  // 确认取出
  async pick(item: waybillInfo, checkCode: any) {
    const money_yuan: number = item.price / 1000
    if (item.price > 0) {
      let params = {
        orderId: item.id,
        cabinetLocationCode: item.cabinetLocationCode,
      }
      /// 是否已付费,true:已付费,false:未付费
      const result = await getOrderIsPay(params)
      if (!result) {

        // 一下代码最想优化代码
      await Modal.confirmCustom(`您的待取包裹超时需要付费的订单，共计${money_yuan}元`, '取出提示', '暂不取出', '支付取出')
      const payResult = await paymentService.pay(params, 'order');
      paymentService.handleOrderPaymentResult(payResult, item,'待取列表');
     

        // 以下代码是之前支付的代码
        // await Modal.confirmCustom(`您的待取包裹超时需要付费的订单，共计${money_yuan}元`, '取出提示', '暂不取出', '支付取出')
        // let info: payDto = {
        //   orderId: item.id,
        //   cabinetLocationCode: item.cabinetLocationCode,
        //   hostIndex: Cache.getItem('hostIndex')
        // }
        // const res = await getPayParams(info)
        // wx.requestPayment({
        //   timeStamp: res.timeStamp,
        //   nonceStr: res.nonceStr,
        //   package: res.packageValue,
        //   signType: res.signType,
        //   paySign: res.paySign,
        //   success: async function () {
        //     let info = {
        //       orderId: item.id,
        //       tradeNo: res.tradeNo,
        //       totalFee: res.totalFee,
        //       cabinetLocationCode: item.cabinetLocationCode,
        //       checkCode: item.checkCode,
        //       type: '待取列表'
        //     }
        
        //     uni.navigateTo({url: '/homePages/storageOrder/storageOrderStatus/storageOrderStatus?info=' + JSON.stringify(info)})
        //   },
        //   fail: function () {
        //   }
        // })
      } else {
        await this.outboundPackage(item, checkCode)
      }
    } else {
      await this.outboundPackage(item, checkCode)
    }
  }

  async outboundPackage(item: waybillInfo, checkCode: any) {
    let that = this
    await Modal.confirmCustom(`确认取出该格口的包裹吗？注意开门后结束寄存`, ` ${item.cabinetBoxLabel} 号格口`,)
    let info: any = {
      cabinetLocationCode: item.cabinetLocationCode,
      orderId: item.id,
      checkCode: checkCode,
      hostIndex: Cache.getItem('hostIndex')
    }
    const res = await outboundOrder(info)
    if (res) {
      let color = config.CONFIG_COLOR
      // @ts-ignore
      this.$showModal({
        title: '关门提示',
        content: '柜门打开成功,请取件后关门',
        showCancel: true,
        cancelText: '柜门未开',
        confirmText: '确认',
        confirmColor: color,
        success(res: any) {
          if (res.confirm) {
            that.getList(true)
          } else {
            that.getList(false)
            let data = {
              cabinetLocationCode: item.cabinetLocationCode,
              checkCode: item.checkCode,
              cabinetBoxLabel: item.cabinetBoxLabel,
              mobile: item.receiverMobile
            }
            let url = `/homePages/receive/noPick/noPick?info=${encodeURIComponent(JSON.stringify(data))}`
            uni.navigateTo({url})
          }
        }
      })
    }
  }

  backHome() {
    uni.switchTab({url: '/pages/home/<USER>/index'})
  }

  loadMore() {
    if (this.packageList.length < this.total) {
      if (this.loadStatus === 'loading') {
        return
      }
      this.loadStatus = 'loading'
      this.current++
      this.getList()
    }
  }

  getKeepTime(time: string) {
    let hours: number = Math.ceil((new Date().getTime() - new Date(time.replace(/-/g, '/')).getTime()) / 1000 / 3600)
    return hours >= 24 ? Math.floor(hours / 24) + '天' + hours % 24 : hours
  }

  // 根据取件码获取订单
  async getOrderByCode() {
    let info = {
      cabinetLocationCode: this.cabinetLocationCode,
      checkCode: this.valArr.val.split("-").join("")
    }
    this.valArr.val = ''
    const res = await orderByCode(info)
    if (res) {
      let checkCode = info.checkCode
      await this.pick(res, checkCode)
    }
  }

  async getList(isGetCount = false) {
    let info: cabinetCodeDto = {
      orderType: 1, // 派件单
      isMobileLast4: true,
    }
    if (this.cabinetLocationId) {
      info = {
        ...info,
        cabinetId: this.cabinetLocationId
      }
    }
    if (this.cabinetLocationCode) {
      info = {
        ...info,
        cabinetLocationCode: this.cabinetLocationCode
      }
    }
    const res = await getOrderListInShop(info)
    if (res) {
      this.packageList = res
    }
  }

  onShow() {
    this.getList()
    this.getLocationInfo()
  }

  onLoad(e: any) {
    if (e.info) {
      try {
        const info: any = JSON.parse(decodeURIComponent(e.info))
        this.cabinetLocationCode = info.cabinetLocationCode
        this.cabinetLocationId = info.cabinetLocationId
      } catch (e) {
      }
    }
  }
}
</script>

<template src="./packageList.html"></template>
<style lang="scss" scoped src="./packageList.scss"></style>
