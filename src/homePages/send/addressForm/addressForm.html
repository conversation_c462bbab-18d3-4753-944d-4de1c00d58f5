<view class="addressForm">
    <view class="border-area">
        <view class="title">
            智能地址识别
        </view>
        <view class="remarks-bg">
            <u-textarea v-model="addressForm.text"
                        placeholder="粘贴文本，系统自动识别，请输入省市区、详细地址、姓名、电话，用空格、逗号或其他符号隔开。如：江苏省南京市江宁区魔咒东路xxx号 小张 130****8953"
                        placeholderClass="placeholder"></u-textarea>
            <view class="space-two padding020">
                <view class="img-select">
                    <!--<text class="icon-l-img iconfont icon-img"></text>-->
                </view>
                <view class="flex-row u-flex-end">
                    <view class="clear" @click="clearText">
                        清空
                    </view>
                    <view class="distinguish" @click="extract">
                        <text>识别</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <view class="border-area2 no-padding">
        <uni-forms ref="subForm" :model-value="addressForm">
            <uni-forms-item name="name">
                <input-widget padding="0" :item-height="100" placeholder="请输入真实姓名" v-model="addressForm.name"
                              label="姓名" :show-clear="true">
                </input-widget>
            </uni-forms-item>
            <uni-forms-item name="mobile">
                <input-widget padding="0" :item-height="100" placeholder="请输入手机号" v-model="addressForm.mobile"
                              label="手机号" :show-clear="true">
                </input-widget>
            </uni-forms-item>
            <uni-forms-item name="pca" @click="locationOrGoSetting">
                <input-widget padding="0" :item-height="100" placeholder="省、市、区" v-model="addressForm.pca"
                              label="所在地区" type="select" :show-clear="true" @select="throttle(openCodePicker)">
                </input-widget>
            </uni-forms-item>
            <uni-forms-item name="address">
                <input-widget padding="0" :item-height="150" placeholder="街道、门牌号"
                              v-model="addressForm.address"
                              label="详细地址" :show-clear="true">
                    <view slot="btn" class="slot-btn" @click="locationOrGoSetting">
                        <text class="iconfont icon-location"></text>
                    </view>
                </input-widget>
            </uni-forms-item>
        </uni-forms>
        <view class="auth1 space-two">
            <view class="flex-row" @click="setDefault">
                <view>
                    <text class="iconfont icons"
                          :class="addressForm.hasDefault ? 'icon-xuanzhong color-active': 'icon-weixuanzhong'"></text>
                </view>
                <view>{{ `设为默认寄件地址` }}</view>
            </view>
            <view class="u-flex-start" @click="clearForm">
                <text>清空</text>
            </view>
        </view>
        <view class="space-two padding200 flex-row">
            <view @click="chooseAddress">
                微信地址
            </view>
        </view>
    </view>
    <!--省市区选择-->
    <regionWindow ref="region" :defaultAddress="pca" @finish="getPca"></regionWindow>
    <view class="bottom">
        <button class="btn auth btn-disable"
                :class="{'active': checkForm}"
                @click="saveAddress">
            <text>立即保存</text>
        </button>
    </view>
</view>
