.addressForm {
  position: relative;
  width: 750upx;
  height: 100%;
  .border-area{
    width: 690upx;
    //height: 360upx;
    background: #FFFFFF;
    border-radius: 10upx;
    margin-top: 30upx;
    margin-left: 30upx;
    box-sizing: border-box;
    padding: 30upx 20upx;
    .title {
      font-size: 30upx;
      font-weight: 500;
      color: #000000;
    }
    .remarks-bg {
      width: 640upx;
      margin-top: 20upx;
      margin-left: 5upx;
      margin-right: 30upx;
      background: #F5F5F5;
      border-radius: 10upx;

      ::v-deep.u-textarea {
        background-color: transparent !important;

        textarea{
          font-size: 26upx !important;
          line-height: 30upx;
          height: 160upx !important;
        }

        .u-textarea__count {
          background-color: transparent !important;
        }
      }
    }
    .icon-img{
      font-size: 42upx;
    }
  }
  .border-area2{
    width: 690upx;
    //height: 360upx;
    background: #FFFFFF;
    border-radius: 10upx;
    margin-top: 30upx;
    margin-left: 30upx;
    box-sizing: border-box;
    padding: 30upx 20upx;
  }
  .no-padding {
    padding: 0upx 20upx;
  }
  .space-two {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  .padding020{
    padding: 0 17upx 17upx 20upx;
    line-height: 36upx;
  }
  ::v-deep.placeholder{
    font-size: 26upx;
    color: #999999;
    font-weight: 400;
    line-height: 30upx;
  }
  .img-select{
    height: 50upx;
    width: 120upx;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .clear {
    height: 50upx;
    width: 120upx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .distinguish{
    width: 120upx;
    height: 50upx;
    background: $config-color;
    border-radius: 25upx;
    display: flex;
    justify-content: center;
    align-items: center;
    text{
      font-size: 26upx;
      font-weight: 400;
      color: #FFFFFF;
    }
  }

  .space-one-start {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }
}
.padding200{
  padding: 0upx 0 30upx 0;
}
.active {
  background: $config-color;
}
.auth1 {
  width: 640upx;
  padding: 36upx 0upx;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  line-height: 44upx;
}
.icons {
   font-size: 32upx;
   margin-right: 12upx;
   color: #999999
 }

.color-blue {
  color: #3087D7;
}

.color-active {
  color: $config-color;
}
