<script lang="ts">
import 'reflect-metadata';
import {Component, Vue} from 'vue-property-decorator';
import Config from "@/utils/config";
import InputWidget from "@/components/input-widget/index.vue";
import UniForms from "@/components/uni-forms/uni-forms.vue";
import UniFormsItem from "@/components/uni-forms-item/uni-forms-item.vue";
import regionWindow from "@/components/region-window/index.vue";
import {Modal} from '@/utils';
import {saveAddress, extractExpress} from "@/model/send";
import {addressDto, addressInfo} from "@/utils/types";
import {CommonMixins} from "@/mixins/commonMixin";

var QQMapWX = require('@/utils/core/qqmap-wx-jssdk.min.js');
var qqmapsdk = new QQMapWX({
  key: Config.QQMAP_KEY
});
@Component({
  name: 'addressForm',
  components: {
    UniForms, UniFormsItem, InputWidget, regionWindow
  }
})
export default class AddressForm extends CommonMixins {
  $refs!: {
    subForm: HTMLFormElement
  }
  private pcaRender: string = ''
  private pcaCode: string[] = []
  private formValidate: boolean = false
  private locationAuth: boolean = false
  private isBack: boolean = false
  private addressType: string = 'send'
  private regexpForm: any = {}
  private pca: any = {
    province: null,
    city: null,
    area: null
  }
  private addressForm: addressInfo = new addressInfo()
  private title: string = '编辑地址'
  private rules: any = {
    name: {
      rules: [{required: true, errorMessage: '请输入姓名', trigger: ['blur', 'change']}, {
        validateFunction: function (rule: any, value: string, data: any, callback: (arg0: string) => void) {
          // const regName = /^[a-zA-Z0-9_\u4e00-\u9fa5]{2,10}$/
          const regName = /^.{2,10}$/
          if (!regName.test(value)) {
            callback('姓名填写有误')
          }
          return true
        }
      }]
    },
    mobile: {
      rules: [{required: true, errorMessage: '请输入手机号'}, {
        validateFunction: function (rule: any, value: string, data: any, callback: (arg0: string) => void) {
          var regIdNo = /^1[3456789]\d{9}$/
          if (!regIdNo.test(value)) {
            callback('手机号格式错误')
          }
          return true
        }
      }]
    },
    pca: {
      rules: [{required: true, errorMessage: '请选择地址'}]
    },
    address: {
      rules: [{required: true, errorMessage: '请输入详细地址'}]
    }
  }

  getPca(e: any) {
    const {province, city, area, street} = e
    this.addressForm.pca = province.name + '/' + city.name + '/' + area.name
    this.addressForm.provinceName = province.name
    this.addressForm.provinceCode = province.areaCode
    this.addressForm.cityName = city.name
    this.addressForm.cityCode = city.areaCode
    this.addressForm.areaName = area.name
    this.addressForm.areaCode = area.areaCode
    this.addressForm.streetName = '-'
    this.addressForm.streetCode = '000000000'
  }

  openCodePicker() {
    // @ts-ignore
    this.$refs.region.open()
  }

  get checkForm() {
    return this.addressForm.name != '' && this.addressForm.mobile != '' && this.addressForm.pca != '' && this.addressForm.address != ''
  }

  saveAddress() {
    this.formValidate = this.checkForm
    if (this.formValidate) {
      this.$refs.subForm.validate().then(async (res: any) => {
        if (res) {
          const result = await saveAddress(this.addressForm)
          if (result) {
            Modal.toast('保存成功')
            if (this.isBack) {
              let pages = getCurrentPages(); //获取所有页面栈实例列表
              let prevPage = pages[pages.length - 2];
              if (this.addressType === 'send') {
                // @ts-ignore
                prevPage.$vm.sender = result
              } else {
                // @ts-ignore
                prevPage.$vm.receiver = result
              }
            }
            uni.navigateBack({delta: 1})
          }
        }
      }).catch((err: any) => {
        console.log(err)
      })
    } else {
      if (this.addressForm.name == '') {
        Modal.toast('请填写真实姓名')
        return false;
      }
      if (this.addressForm.mobile == '') {
        Modal.toast('请填写正确的手机号')
        return false;
      }
      if (this.addressForm.pca == '') {
        Modal.toast('请选择所在地区')
        return false;
      }
      if (this.addressForm.address == '') {
        Modal.toast('请填写详细地址')
        return false;
      }
    }
  }

  clearText() {
    this.addressForm.text = ''
  }

  async extract() {
    if (this.addressForm.text) {
      let isAddress: boolean = RegExp(/.+?(省|市|自治区|自治州|县|区)/g).test(this.addressForm.text)
      if (isAddress) {
        const res = await extractExpress({text: this.addressForm.text})
        if (res) {
          this.addressForm.name = res.name
          this.addressForm.mobile = res.mobile
          this.addressForm.provinceName = res.province
          this.addressForm.provinceCode = '000000'
          this.addressForm.cityName = res.city
          this.addressForm.cityCode = '000000'
          this.addressForm.areaName = res.area
          this.addressForm.areaCode = '000000'
          this.addressForm.streetName = res.street
          this.addressForm.streetCode = '000000000'
          this.addressForm.address = res.address
          this.addressForm.pca = res.province + '/' + res.city + '/' + res.area
        }
      } else {
        Modal.toast('请按照规定填写省市区，地址等信息')
      }
    }
  }

  clearForm() {
    this.addressForm = new addressInfo()
  }

  setDefault() {
    this.addressForm.hasDefault = this.addressForm.hasDefault ? 0 : 1
  }

  locationOrGoSetting() {
    if (this.locationAuth) {
      // this.getLocation()
      this.chooseLocation()
    } else {
      uni.openSetting({})
    }
  }

  chooseLocation() {
    var that = this
    uni.chooseLocation({
      success: function (res: any) {
        qqmapsdk.reverseGeocoder({
          location: {
            latitude: res.latitude,
            longitude: res.longitude
          },
          success: function (res_: any) {
            let province = res_.result.ad_info.province
            let city = res_.result.ad_info.city
            let area = res_.result.ad_info.district
            let street = res_.result.address_reference.town.title
            let streetCode = res_.result.address_reference.town.id
            let provinceCode = streetCode.substr(0, 2) + '0000'
            let cityCode = streetCode.substr(0, 4) + '00'
            let areaCode = streetCode.substr(0, 6)
            let applyVillage = res_.result.address_reference.landmark_l2.title
            // let address = res_.result.address + res_.result.formatted_addresses.recommend
            let address = applyVillage
            let lat = res_.result.location.lat
            let lng = res_.result.location.lng
            that.addressForm.provinceName = province
            that.addressForm.provinceCode = provinceCode
            that.addressForm.cityName = city
            that.addressForm.cityCode = cityCode
            that.addressForm.areaName = area
            that.addressForm.areaCode = areaCode
            that.addressForm.streetName = '-'
            that.addressForm.streetCode = '000000000'
            that.addressForm.address = address
            that.pca = {
              province: provinceCode,
              city: cityCode,
              area: areaCode
            }
            that.addressForm.pca = province + '/' + city + '/' + area
          },
          fail: function (err: any) {
          },
          complete: function (res: any) {
          }
        });
        console.log(that.addressForm)
      },
      fail: function () {

      }
    })
  }

  chooseAddress() {
    var that = this
    uni.getSetting({
      success: function (result) {
        if (result.authSetting['scope.address']) {
          uni.chooseAddress({
            success: function (res) {
              console.log(res);
              // @ts-ignore
              let code = res.nationalCodeFull ? res.nationalCodeFull : res.nationalCode
              let provinceCode = '000000'
              let cityCode = '000000'
              let areaCode = '000000'
              if (code.length === 6) {
                provinceCode = code.substr(0, 2) + '0000'
                cityCode = code.substr(0, 4) + '00'
                areaCode = code.substr(0, 6)
              }
              that.addressForm.name = res.userName
              that.addressForm.mobile = res.telNumber
              that.addressForm.provinceName = res.provinceName
              that.addressForm.provinceCode = provinceCode
              that.addressForm.cityName = res.cityName
              that.addressForm.cityCode = cityCode
              that.addressForm.areaName = res.countyName
              that.addressForm.areaCode = areaCode
              that.addressForm.streetName = '-'
              that.addressForm.streetCode = '000000000'
              // @ts-ignore
              that.addressForm.address = res.detailInfoNew ? res.detailInfoNew : res.detailInfo
              that.pca = {
                province: provinceCode,
                city: cityCode,
                area: areaCode
              }
              that.addressForm.pca = res.provinceName + '/' + res.cityName + '/' + res.countyName
            }
          })
        }
      }
    })
  }

  // formatData(text: string) {
  //   const list: string[] = text.split(/[\s|\n|,|，]+/g)
  //   console.log('list', list);
  // }

  onShow() {
    const that = this
    uni.authorize({
      scope: 'scope.userLocation',
      success() {
        that.locationAuth = true
        // that.getLocation()
      },
      fail() {
        that.locationAuth = false
      }
    })
    // uni.getClipboardData({
    //   success: function (res) {
    //     console.log('====', res);
    //     if (res.data && res.data.length > 0) {
    //       that.formatData(res.data)
    //       // Modal.toast('{{ BrandName }}获取了您剪切板的内容')
    //       // uni.setClipboardData({data: '', showToast: false})
    //     } else {
    //       console.log('未读取')
    //     }
    //   }
    // });
  }

  onLoad(e: any) {
    e.type && (this.addressType = e.type)
    e.isBack && (this.isBack = e.isBack)
    if (e.addressInfo) {
      this.addressForm = JSON.parse(decodeURIComponent(e.addressInfo))
      this.pca = {
        province: this.addressForm.provinceCode,
        city: this.addressForm.cityCode,
        area: this.addressForm.areaCode
      }
      this.addressForm.pca = this.addressForm.provinceName + '/' + this.addressForm.cityName + '/' + this.addressForm.areaName
    }
    this.$refs.subForm.setRules(this.rules)
  }
}
</script>

<template src="./addressForm.html"></template>
<style src="./addressForm.scss" lang="scss" scoped></style>
