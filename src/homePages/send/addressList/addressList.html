<div class="addressList">
    <view class="top">
        <search-input v-model="keyword" placeholder="请输入姓名/手机号/公司名称" @inputConfirm="getList"
                      :show-scan="false"></search-input>
    </view>
    <scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore">
        <view v-if="list.length>0">
            <view v-for="(item, index) in list" :key="index">
                <view class="item">
                    <view @click="throttle(chooseAddress, item)">
                        <view class="space-two padding-item">
                            <view class="flex-row user-info">
                                <text>{{ getUser(item) }}</text>
                            </view>
                        </view>
                        <view style="padding: 6upx"></view>
                        <view class="space-one-start padding-item">
                            <view class="flex-row user-address">
                                <text>{{ item.address }}</text>
                            </view>
                        </view>
                        <view class="border-sp"></view>
                    </view>
                    <view class="space-two padding-item">
                        <view class="flex-row flex-start fsize" @click="throttle(switchChange,item.id)">
                            <switch :checked="item.hasDefault===1" :color="configColor" disabled="" />
                            默认寄件地址
                        </view>
                        <view class="flex-row">
                            <view class="flex-row edit" @click="throttle(deleteAddress,item.id)">
                                <text class="iconfont icon-delete"></text>
                                <span class="pl5"></span>
                                删除
                            </view>
                            <view class="flex-row edit" @click="throttle(editAddress,item)">
                                <text class="iconfont icon-bianji"></text>
                                <span class="pl5"></span>
                                编辑
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <u-loadmore :status="loadStatus" margin-top="20" bg-color="#f6f6f6" font-size="13" />
        </view>
        <view class="no-result" v-else>
            <text class="text">暂无数据哦~</text>
        </view>
    </scroll-view>
    <view class="bottom">
        <button class="btn auth"
                @click="addNewAddress()">
            <text>添加新地址</text>
        </button>
    </view>
</div>
