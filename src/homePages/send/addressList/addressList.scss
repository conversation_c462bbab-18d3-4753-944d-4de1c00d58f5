.addressList {
  position: relative;
  width: 750upx;
  height: 100%;
  .top{
    position: fixed;
    background-color: #ffffff;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
  }

  // 地址码显示区域
  .address-code-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20upx 30upx;
    background-color: white;

    .address-code-content {
      display: flex;
      align-items: center;

      .address-code-label {
        font-size: 28upx;
        color: #666666;
        margin-right: 10upx;
      }

      .address-code-value {
        font-size: 32upx;
        font-weight: 600;
        color: $config-color;
        letter-spacing: 2upx;
      }
    }

    .copy-btn {
      display: flex;
      align-items: center;
      padding: 12upx 20upx;
      background-color: $config-color;
      border-radius: 20upx;

      .copy-text {
        font-size: 24upx;
        color: #ffffff;
      }
    }
  }
  .scroll-view{
    position: absolute;
    top: 120upx;
    left: 0;
    right: 0;
    bottom: 0;
    padding-top: 10upx;

    // 当有地址码区域时，调整top值
    &.scroll-view-with-code {
      top: 220upx;
    }
    .item{
      width: 690upx;
      margin-left: 30upx;
      background: #FFFFFF;
      margin-bottom: 20upx;
      border-radius: 10upx;
      padding:  24upx 0 15upx 0;
      box-sizing: border-box;
      .padding-item {
        padding:  0 30upx;
      }
      .fsize{
        font-size: 24upx;
        color: #B5B5B5;
      }
      switch{
        margin-left: -24upx;
        margin-right: -14upx;
        transform: scale(0.5,0.5);
      }
      .border-sp{
        width: 100%;
        margin: 15upx 0;
        border-top: 1upx solid #F5F5F5;
      }

      .user-info {
        font-size: 30upx;
        font-weight: 500;
        color: #000000;
      }
      .user-address{
        font-size: 24upx;
        font-weight: 400;
        color: #585858;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .edit {
        font-size: 24upx;
        display: flex;
        margin-left: 30upx;
        align-items: center;
        color: $config-color;

      }
    }
  }
}
.space-two {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
