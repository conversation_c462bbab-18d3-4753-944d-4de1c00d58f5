<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import Config from '@/utils/config'
import config from '@/utils/config'
import {addressDto, userDto} from "@/utils/types";
import {addressPage, defaultAddress, deleteAddress} from "@/model/send";
import {getUserInfo} from "@/model/login";
import SearchInput from "@/components/search-input/index.vue";
import {Modal, Cache} from "@/utils";
import {CommonMixins} from "@/mixins/commonMixin";

@Component({
  name: 'addressList',
  components: {SearchInput}
})
export default class AddressList extends CommonMixins {
  private configColor=config.CONFIG_COLOR
  private keyword: string = ''
  private addressType: string = 'send'
  private list: addressDto[] = []
  private current: number = 1
  private total: number = 0
  private loadStatus: string = 'loadmore'
  private user: userDto | null = null
  private addressCode: string = ''

  onLoad(e: any) {
    e.type && (this.addressType = e.type)
    uni.setNavigationBarTitle({title: Config.AddressType[this.addressType]})
  }

  chooseAddress(item: any) {
    console.log('choose', item)
    let pages = getCurrentPages(); //获取所有页面栈实例列表
    let prevPage = pages[pages.length - 2];
    if (this.addressType === 'send') {
      // @ts-ignore
      prevPage.$vm.sender = item
    } else {
      // @ts-ignore
      prevPage.$vm.receiver = item
    }
    uni.navigateBack({delta: 1})
  }

  editAddress(item: any) {
    console.log(item);
    this.addNewAddress(Object.keys(item).includes('$orig') ? item.$orig : item)
  }

  addNewAddress(item?: any) {
    let url: string = '/homePages/send/addressForm/addressForm'
    if (item?.name) {
      url += `?addressInfo=${encodeURIComponent(JSON.stringify(item))}`
    }
    uni.navigateTo({url: url})
  }

  async deleteAddress(id: string) {
    await Modal.confirm('是否删除？')
    const result = await deleteAddress({id: id})
    if (result) {
      Modal.toast('删除成功')
      await this.getList()
    }
  }

  async switchChange(id: string) {
    const result = await defaultAddress({id: id})
    if (result) {
      Modal.toast('设置成功')
      await this.getList()
    }
  }

  getUser(item: any) {
    return item.name + '\u3000' + item.mobile
  }

  // 获取用户信息并生成地址码
  async getUserInfo() {
    try {
      let user = await Cache.getItem('user');
      if (!user) {
        user = await getUserInfo();
        Cache.setItem('user', user);
      }
      this.user = user;
      this.generateAddressCode();
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  }

  // 生成地址码（基于用户ID的简化版本）
  generateAddressCode() {
    if (this.user && this.user.id) {
      // 使用用户ID的后8位作为地址码，确保唯一性
      const userId = this.user.id.toString();
      this.addressCode = userId.slice(-8).toUpperCase();
    }
  }

  // 复制地址码
  copyAddressCode() {
    if (this.addressCode) {
      uni.setClipboardData({
        data: this.addressCode,
        success: () => {
          Modal.toast('地址码已复制到剪贴板');
        },
        fail: () => {
          Modal.toast('复制失败，请重试');
        }
      });
    }
  }

  // 复制地址详情和地址码
  copyAddressDetail(item: any) {
    const addressDetail = `${item.name} ${item.mobile}\n${item.address}\n地址码：${this.addressCode}`;
    uni.setClipboardData({
      data: addressDetail,
      success: () => {
        Modal.toast('地址详情已复制到剪贴板');
      },
      fail: () => {
        Modal.toast('复制失败，请重试');
      }
    });
  }

  async getList() {
    const info: any = {
      keyword: this.keyword,
      current: this.current,
      size: 10
    }
    addressPage(info).then(res => {
      if (res) {
        this.total = res.total * 1
        if (this.current === 1) {
          this.list = res.records
        } else {
          this.list = this.list.concat(res.records)
        }
        if (this.list.length === this.total) {
          this.loadStatus = 'noMore'
        } else {
          this.loadStatus = 'loadmore'
        }
      }
    })
  }


  loadMore(e: any) {
    if (this.list.length < this.total) {
      if (this.loadStatus === 'loading') {
        return
      }
      this.loadStatus = 'loading'
      this.current++
      this.getList()
    }
  }

  async onShow() {
    await this.getUserInfo()
    this.getList()
  }

  resetCurrent() {
    this.current = 1
    this.loadStatus = 'loadmore'
  }

  async onPullDownRefresh() {
    this.resetCurrent()
    await this.getList()
    uni.stopPullDownRefresh()
  }
}
</script>

<template src="./addressList.html"></template>
<style src="./addressList.scss" lang="scss" scoped></style>
