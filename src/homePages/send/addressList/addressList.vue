<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import Config from '@/utils/config'
import config from '@/utils/config'
import {addressDto} from "@/utils/types";
import {addressPage, defaultAddress, deleteAddress} from "@/model/send";
import SearchInput from "@/components/search-input/index.vue";
import {Modal} from "@/utils";
import {CommonMixins} from "@/mixins/commonMixin";

@Component({
  name: 'addressList',
  components: {SearchInput}
})
export default class AddressList extends CommonMixins {
  private configColor=config.CONFIG_COLOR
  private keyword: string = ''
  private addressType: string = 'send'
  private list: addressDto[] = []
  private current: number = 1
  private total: number = 0
  private loadStatus: string = 'loadmore'

  onLoad(e: any) {
    e.type && (this.addressType = e.type)
    uni.setNavigationBarTitle({title: Config.AddressType[this.addressType]})
  }

  chooseAddress(item: any) {
    console.log('choose', item)
    let pages = getCurrentPages(); //获取所有页面栈实例列表
    let prevPage = pages[pages.length - 2];
    if (this.addressType === 'send') {
      // @ts-ignore
      prevPage.$vm.sender = item
    } else {
      // @ts-ignore
      prevPage.$vm.receiver = item
    }
    uni.navigateBack({delta: 1})
  }

  editAddress(item: any) {
    console.log(item);
    this.addNewAddress(Object.keys(item).includes('$orig') ? item.$orig : item)
  }

  addNewAddress(item?: any) {
    let url: string = '/homePages/send/addressForm/addressForm'
    if (item?.name) {
      url += `?addressInfo=${encodeURIComponent(JSON.stringify(item))}`
    }
    uni.navigateTo({url: url})
  }

  async deleteAddress(id: string) {
    await Modal.confirm('是否删除？')
    const result = await deleteAddress({id: id})
    if (result) {
      Modal.toast('删除成功')
      await this.getList()
    }
  }

  async switchChange(id: string) {
    const result = await defaultAddress({id: id})
    if (result) {
      Modal.toast('设置成功')
      await this.getList()
    }
  }

  getUser(item: any) {
    return item.name + '\u3000' + item.mobile
  }

  async getList() {
    const info: any = {
      keyword: this.keyword,
      current: this.current,
      size: 10
    }
    addressPage(info).then(res => {
      if (res) {
        this.total = res.total * 1
        if (this.current === 1) {
          this.list = res.records
        } else {
          this.list = this.list.concat(res.records)
        }
        if (this.list.length === this.total) {
          this.loadStatus = 'noMore'
        } else {
          this.loadStatus = 'loadmore'
        }
      }
    })
  }


  loadMore(e: any) {
    if (this.list.length < this.total) {
      if (this.loadStatus === 'loading') {
        return
      }
      this.loadStatus = 'loading'
      this.current++
      this.getList()
    }
  }

  onShow() {
    this.getList()
    // uni.setClipboardData({
    //   data: '张三 13625630713 安徽省合肥市蜀山区锦绣大道158号合肥学院新区'
    // })
  }

  resetCurrent() {
    this.current = 1
    this.loadStatus = 'loadmore'
  }

  async onPullDownRefresh() {
    this.resetCurrent()
    await this.getList()
    uni.stopPullDownRefresh()
  }
}
</script>

<template src="./addressList.html"></template>
<style src="./addressList.scss" lang="scss" scoped></style>
