<div class="baranSelect">
    <view class="top">
        <text class="iconfont icon-notice1"></text>
        <span class="pl15"></span>
        格口费用为首重价格，如超重快递公司会加收续重费！
    </view>
    <view class="list-title">
        <view class="flex-row justify-between">
            <view>选择快递公司及格口大小</view>
            <view class="right" @click="goFeeDesc">
                <text class="iconfont icon-notice icon"></text>
                格口及费用说明
            </view>
        </view>
    </view>
    <scroll-view scroll-y class="scroll-view">
        <view v-if="list.length>0">
            <view v-for="(item, index) in list" :key="index">
                <view class="item">
                    <view class="flex-row start">
                        <img :src="getCompanyLogo(item.brandCode)"
                             class="logo"
                             alt="">
                        <view>{{ getCompanyName(item.brandCode) }}</view>
                    </view>
                    <view class="flex-row gk-view">
                        <view class="gk-list flex-row">
                            <view :style="{'background-color': bgColor(item.brandCode, item.microCount, 6)}" class="flex-column gk-item"
                                  @click="chooseBox(item.microCount, item.microPrice, item.brandCode, 6)">
                                <text>
                                    极小格
                                </text>
                                <text :style="{'color': textColor(item.brandCode, item.microCount, 6)}">
                                    ￥{{ item.microPrice }}
                                </text>
                            </view>
                            <view class="flex-column gk-item" :style="{'background-color': bgColor(item.brandCode, item.miniCount, 5)}"
                                  @click="chooseBox(item.miniCount, item.miniPrice, item.brandCode, 5)">
                                <text>
                                    超小格
                                </text>
                                <text :style="{'color': textColor(item.brandCode, item.miniCount, 5)}">
                                    ￥{{ item.miniPrice }}
                                </text>
                            </view>
                            <view class="gk-item flex-column"
                                  :style="{'background-color': bgColor(item.brandCode, item.smallCount,4)}"
                                  @click="chooseBox(item.smallCount, item.smallPrice, item.brandCode, 4)">
                                <text>
                                    小格
                                </text>
                                <text :style="{'color': textColor(item.brandCode, item.smallCount, 4)}">
                                    ￥{{ item.smallPrice }}
                                </text>
                            </view>
                            <view class="gk-item flex-column"
                                  :style="{'background-color': bgColor(item.brandCode, item.mediumCount, 3)}"
                                  @click="chooseBox(item.mediumCount,item.mediumPrice, item.brandCode, 3)">
                                <text>
                                    中格
                                </text>
                                <text :style="{'color': textColor(item.brandCode, item.mediumCount, 3)}">
                                    ￥{{ item.mediumPrice }}
                                </text>
                            </view>
                            <view class="gk-item flex-column"
                                  :style="{'background-color': bgColor(item.brandCode, item.largeCount, 2)}"
                                  @click="chooseBox(item.largeCount, item.largePrice, item.brandCode, 2)">
                                <text>
                                    大格
                                </text>
                                <text :style="{'color': textColor(item.brandCode, item.largeCount, 2)}">
                                    ￥{{ item.largePrice }}
                                </text>
                            </view>
                            <view class="gk-item flex-column"
                                  :style="{'background-color': bgColor(item.brandCode, item.hugeCount, 1)}"
                                  @click="chooseBox(item.hugeCount,item.hugePrice, item.brandCode, 1)">
                                <text>
                                    超大格
                                </text>
                                <text :style="{'color': textColor(item.brandCode, item.hugeCount, 1)}">
                                    ￥{{ item.hugePrice }}
                                </text>
                            </view>
                            <view :style="{'background-color': bgColor(item.brandCode, item.superCount, 0)}" class="flex-column gk-item"
                                  @click="chooseBox(item.superCount, item.superPrice, item.brandCode, 0)">
                                <text>
                                    极大格
                                </text>
                                <text :style="{'color': textColor(item.brandCode, item.superCount, 0)}">
                                    ￥{{ item.superPrice }}
                                </text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <u-loadmore :status="loadStatus" margin-top="20" bg-color="#f6f6f6" font-size="13" />
        </view>
        <view class="no-result" v-else>
        </view>
    </scroll-view>
    <view class="bottom">
        <button class="btn btn-padding"
                :class="{'auth': isSelect}"
                @click="confirmSelect()">
            <text>确定</text>
        </button>
    </view>
</div>
