.baranSelect {

  position: relative;
  width: 750upx;
  height: 100vh;

  .top{
    background-color: #FFF7F2;
    width: 750upx;
    height: 76upx;
    display: flex;
    align-items: center;
    color: $config-color;
    padding-left: 30upx;
    font-size: 24upx;
  }
  .list-title{
    padding: 20upx 30upx;
    font-size: 24upx;

    .right{
      color: $config-color;
      .icon{
        font-size: 22upx;
        padding-right: 10upx;
      }
    }
  }
  .scroll-view {
    position: absolute;
    top: 150upx;
    left: 0;
    right: 0;
    bottom: 0;
    padding-top: 10upx;
    box-sizing: border-box;

    .item {
      width: 690upx;
      margin-left: 30upx;
      max-height: 348upx;
      background: #FFFFFF;
      margin-bottom: 20upx;
      border-radius: 10upx;
      padding: 24upx 30upx;
      box-sizing: border-box;
      .logo {
        width: 72upx;
        height: 72upx;
        margin-right: 10upx;
      }
      .start {
        justify-content: flex-start;
        align-items: center;
      }
      .gk-view{
        margin-top: 12upx;
        display: flex;
        border: 1upx solid #E5E5E5;
        border-radius: 10upx;
        align-items: center;
      }
      .gk-list{
        flex:1;
        height: 136upx;
        background-color: #E5E5E5;
      }

      .primary-bg{
        background-color: $config-color;
      }
      .gk-item{
        flex:1;
        justify-content: center;
        align-items: center;
        border-right: 1px solid #EEEEED;
        text:first-child {
          font-size: 28upx;
          font-weight: 400;
        }
        text:last-child {
          font-size: 24upx;
          font-weight: 400;
          color: $config-color;
        }
      }
      .gk-item:first-child {
        border-top-left-radius: 10upx;
        border-bottom-left-radius: 10upx;
      }
      .gk-item:last-child {
        flex: 1;
        justify-content: center;
        align-items: center;
        border-top-right-radius: 10upx;
        border-bottom-right-radius: 10upx;
        border-right: none;
      }
    }
  }
}
