<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import {CommonMixins} from "@/mixins/commonMixin";
import {cabinetForm} from "@/utils/types";
import {usableSummary} from "@/model/send";
import {Modal, Utils} from "@/utils";
import Config from "@/utils/config";
import config from "@/utils/config";

@Component({
  name: 'BrandSelect'
})
export default class BrandSelect extends CommonMixins {
  private cabinet: cabinetForm = new cabinetForm()
  private list: any[] = []
  private boxType: number = -1
  private price: number = 0
  private brandCode: string = ''

  get bgColor() {
    return function (code: string, count: number, boxType: number) {
      return this.getColor(code, count, boxType)
    }
  }

  get textColor() {
    return function (code: string, count: number, boxType: number) {
      return this.getTextColor(code, count, boxType)
    }
  }

  get isSelect() {
    return this.brandCode !== '' && this.boxType > -1
  }

  getCount(count: number) {
    if (count > 0) {
      return count
    }
    return '-'
  }

  getColor(code: string, count: number, boxType: number) {
    let color = config.CONFIG_COLOR
    if (count > 0) {
      if (boxType === this.boxType && code === this.brandCode) {
        return color
      }
      return '#ffffff'
    } else {
      return 'transparent'
    }
  }

  getTextColor(code: string, count: number, boxType: number) {
    let color = config.CONFIG_COLOR
    if (count > 0) {
      if (boxType === this.boxType && code === this.brandCode) {
        return '#ffffff'
      }
      return color
    } else {
      return 'black'
    }
  }

  chooseBox(count: number, price: number, brandCode: string, boxType: number) {
    if (count > 0) {
      this.boxType = boxType
      this.price = price
      this.brandCode = brandCode
    } else {
      Modal.toast('该类型暂无空闲格口')
    }
  }

  getCompanyLogo(code: string) {
    return Utils.getCompanyLogo(code)
  }

  getCompanyName(code: string) {
    return Config.EXPRESS[code]
  }

  goFeeDesc() {
    uni.navigateTo({url: '/homePages/send/feeDesc/feeDesc'})
  }

  async getBoxUsable() {
    const res = await usableSummary({cabinetLocationCode: this.cabinet.code})
    if (res) {
      this.cabinet = {...this.cabinet, ...res}
      const sendJson: any = JSON.parse(this.cabinet.sendJson)
      this.list = []
      Object.keys(sendJson).forEach((key) => {
        this.list.push({
          brandCode: key,
          superCount: this.cabinet.superCount,
          superPrice: ((sendJson[key].supers / 1000) || 0).toFixed(2),
          hugeCount: this.cabinet.hugeCount,
          hugePrice: (sendJson[key].huge / 1000).toFixed(2),
          largeCount: this.cabinet.largeCount,
          largePrice: (sendJson[key].large / 1000).toFixed(2),
          mediumCount: this.cabinet.mediumCount,
          mediumPrice: (sendJson[key].medium / 1000).toFixed(2),
          smallCount: this.cabinet.smallCount,
          smallPrice: (sendJson[key].small / 1000).toFixed(2),
          miniCount: this.cabinet.miniCount,
          miniPrice: (sendJson[key].mini / 1000).toFixed(2),
          microCount: this.cabinet.microCount,
          microPrice: ((sendJson[key].micro / 1000) || 0).toFixed(2),
        })
      })
    }
  }

  confirmSelect() {
    if (this.isSelect) {
      let pages = getCurrentPages(); //获取所有页面栈实例列表
      let prevPage = pages[pages.length - 2];
      // @ts-ignore
      prevPage.$vm.brandInfo = {brandCode: this.brandCode, boxType: this.boxType, price: this.price}
      uni.navigateBack({delta: 1})
    } else {
      Modal.toast('请选择格口')
    }
  }

  onLoad(e: any) {
    if (e.cabinetInfo) {
      this.cabinet = JSON.parse(decodeURIComponent(e.cabinetInfo))
      this.getBoxUsable()
    }
  }

}
</script>

<template src="./brandSelect.html"></template>
<style lang="scss" scoped src="./brandSelect.scss"></style>
