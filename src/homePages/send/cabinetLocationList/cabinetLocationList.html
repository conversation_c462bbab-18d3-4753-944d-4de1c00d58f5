<view class="container">
    <view class="top-1">
        <text>请选择要下单的柜机:</text>
    </view>
    <scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore">
        <view v-if="list.length>0">
            <view v-for="(item, index) in list" :key="index" @click="toAddressForm(item)">
                <view class="item">
                    <view class="flex-row between">
                        <view>{{ item.name }}</view>
                        <view class="distance">
                            <text class="iconfont icon-location icon"></text>{{ getDistance(item.distance) }}</view>
                    </view>
                    <view class="flex-row gk-view">
                        <view class="gk-list flex-row">
                            <view class="flex-column gk-item">
                                <text>
                                    {{ getCount(item.microCount) }}
                                </text>
                                <text>
                                    极小格
                                </text>
                            </view>
                            <view class="flex-column gk-item">
                                <text>
                                    {{ getCount(item.miniCount) }}
                                </text>
                                <text>
                                    超小格
                                </text>
                            </view>
                            <view class="gk-item flex-column">
                                <text>
                                    {{ getCount(item.smallCount) }}
                                </text>
                                <text>
                                    小格
                                </text>
                            </view>
                            <view class="gk-item flex-column">
                                <text>
                                    {{ getCount(item.mediumCount) }}
                                </text>
                                <text>
                                    中格
                                </text>
                            </view>
                            <view class="gk-item flex-column">
                                <text>
                                    {{ getCount(item.largeCount) }}
                                </text>
                                <text>
                                    大格
                                </text>
                            </view>
                            <view class="gk-item flex-column">
                                <text>
                                    {{ getCount(item.hugeCount) }}
                                </text>
                                <text>
                                    超大格
                                </text>
                            </view>
                            <view class="gk-item flex-column">
                                <text>
                                    {{ getCount(item.superCount) }}
                                </text>
                                <text>
                                    极大格
                                </text>
                            </view>
                        </view>
                    </view>
                    <view class="choose-view">
                        <view class="choose-btn">选择</view>
                    </view>
                </view>
            </view>
            <u-loadmore :status="loadStatus" margin-top="20" bg-color="#f6f6f6" font-size="13" />
        </view>
        <view class="no-result" v-else>
        </view>
    </scroll-view>
</view>
