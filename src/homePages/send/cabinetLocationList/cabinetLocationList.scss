.container {
  position: relative;
  width: 750upx;
  height: 100vh;
  .top-1{
    height: 56upx;
    line-height: 56upx;
    width: 100vw;
    font-size: 28upx;
    padding-left: 20upx;
    padding-top: 8upx;
    padding-bottom: 8upx;
    background-color: #ffffff;
  }
  .scroll-view{
    position: absolute;
    top: 70upx;
    left: 0;
    right: 0;
    bottom: 0;
    padding-top: 10upx;
    box-sizing: border-box;
    .item{
      width: 690upx;
      margin-left: 30upx;
      max-height: 348upx;
      background: #FFFFFF;
      margin-bottom: 20upx;
      border-radius: 10upx;
      padding:  24upx 30upx;
      box-sizing: border-box;
      .between{
        justify-content: space-between;
      }
      .gk-view{
        margin-top: 12upx;
        display: flex;
        align-items: center;
      }
      .gk-list{
        flex:1;
        height: 136upx;
        background-color: #E5E5E5;
        border-radius: 10upx;
      }
      .gk-item{
        flex:1;
        justify-content: center;
        align-items: center;
        border-right: 1px solid #EEEEED;
        text:first-child {
          font-size: 36upx;
          font-weight: 400;
        }
        text:last-child {
          font-size: 22upx;
          font-weight: 400;
        }
      }
      .gk-item:last-child {
        flex: 1;
        justify-content: center;
        align-items: center;
        border-right: none;
      }
      .choose-view{
        display: flex;
        justify-content: flex-end;
        margin-top: 20upx;
      }
      .choose-btn{
       width: 180upx;
        height: 60upx;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: $config-color;
        color: #ffffff;
        border-radius: 30upx;
      }
      .distance{
        font-size: 30upx;
        font-weight: 400;
        color: $config-color;
        text-align: end;
        .icon {
        font-size: 32upx;
      }
      }
    }
  }
}
