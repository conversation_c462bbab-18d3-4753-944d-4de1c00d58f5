<script lang="ts">
import 'reflect-metadata';
import {Component, Vue} from 'vue-property-decorator';
import {locationDto, longLatDto} from "@/utils/types";
import {getCabinetList} from "@/model/cabinet";
import {Cache} from "@/utils";

@Component({
  name: 'CabinetLocationList'
})
export default class CabinetLocationList extends Vue {
  private locationAuth: boolean = false
  private isBack: boolean = false
  private location: locationDto = {
    latitude: 0,
    longitude: 0
  };
  private list: any = []
  private loadStatus: string = 'loadmore'
  private total: number = 0
  private isRealName: boolean = false
  private current: number = 1

  getLocation() {
    var that = this
    uni.getLocation({
      type: 'gcj02',
      success: function (res) {
        that.location = res
        that.getList()
      },
      fail: function (err) {
      }
    })
  }

  toAddressForm(item: any) {
    Cache.removeItem('hostIndex')
    if (this.isRealName) {
      if (this.isBack) {
        let pages = getCurrentPages(); //获取所有页面栈实例列表
        let prevPage = pages[pages.length - 2];
        // @ts-ignore
        prevPage.$vm.cabinet = item
        uni.navigateBack({delta: 1})
      } else {
        let data={
          name:item.name,
          code:item.code,
          sendJson:item.sendJson
        }
        uni.navigateTo({url: '/homePages/send/sendForm/sendForm?cabinetInfo=' + encodeURIComponent(JSON.stringify(data))})
      }
    } else {
      uni.navigateTo({url: '/myPages/realNameAuth/realNameAuth?cabinetInfo=' + encodeURIComponent(JSON.stringify(item))})
    }
  }

  async getList() {
    let info: longLatDto = {
      latitude: this.location.latitude,
      longitude: this.location.longitude,
      switchSend: 1
    }
    this.list = []
    var res = await getCabinetList(info)
    if (res) {
      this.total = res.total * 1
      if (this.current === 1) {
        this.list = res
      } else {
        this.list = this.list.concat(res)
      }
      this.loadStatus = 'noMore'
    }
    // if (res) {
    //   const data_ = JSON.parse(JSON.stringify(res))
    //   this.list = data_
    // }
  }

  getCount(count: number) {
    if (count > 0) {
      return count
    }
    return '-'

  }

  getDistance(distance: number) {
    let str = ''
    if (distance >= 1000) {
      str = (distance / 1000).toFixed(1) + 'km'
    } else {
      str = distance.toFixed(0) + 'm'
    }
    return str
  }

  loadMore(e: any) {
    if (this.list.length < this.total) {
      if (this.loadStatus === 'loading') {
        return
      }
      this.loadStatus = 'loading'
      this.current++
      this.getList()
    }
  }


  resetCurrent() {
    this.current = 1
    this.loadStatus = 'loadmore'
  }

  onShow() {
    var that = this
    // this.getUserSetting()
    uni.authorize({
      scope: 'scope.userLocation',
      success() {
        that.locationAuth = true
        that.getLocation()
      },
      fail() {
        that.locationAuth = false
      }
    })
  }

  onLoad(e: any) {
    if (e) {
      if (e.isBack) {
        this.isBack = e.isBack
      }
    }
    const user = Cache.getItem('user')
    if (user) {
      this.isRealName = user.hasReal === 1
    }
  }

  async onPullDownRefresh() {
    this.resetCurrent()
    await this.getList()
    uni.stopPullDownRefresh()
  }
}
</script>

<template src="./cabinetLocationList.html"></template>
<style src="./cabinetLocationList.scss" lang="scss" scoped></style>
