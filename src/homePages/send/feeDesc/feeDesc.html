<div class="feeDesc">
    <view class="page-padding">
        <view class="title-1">【格口及费用说明】</view>
        <view class="title-desc">
            {{ BrandName }}格口分为小/中/大等不同尺寸，不同型号柜机格口尺寸可能有差异，格口大小以实际为准
        </view>
        <view class="title-2">
            1.格口尺寸说明
            <span class="desc">仅供参考</span>
        </view>

    </view>
    <img :src="getImage('cabinet')" alt="">
    <view class="page-padding">
        <view class="flex-row">
            <view v-for="(item, index) in boxList" :key="index" class="box-item">
                <view class="name">{{ item.name }}</view>
                <view class="desc">{{ item.desc }}</view>
                <view class="package-desc">{{ item.packageDesc }}</view>
            </view>
        </view>
        <view class="title-2">
            2.寄件费用说明
        </view>
        <view class="title-desc">
            投递入柜时，智能柜先按 小/中/大 格口标注的费用收取物品首重的价格，快递员取出物品后，若取出的包裹<span class="primary-color font-bold">超重</span>，
            快递员会联系您<span class="primary-color font-bold">加收超重费</span>或者退回。
        </view>
    </view>
    <view class="bottom">
        <button class="btn auth active"
                @click="goBack">
            <text>好的</text>
        </button>
    </view>
</div>
