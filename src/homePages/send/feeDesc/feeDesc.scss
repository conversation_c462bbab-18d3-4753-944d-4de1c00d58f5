.feeDesc {
  position: relative;
  width: 750upx;
  height: 100vh;
  background-color: white;
  .page-padding{
    padding:  0 30upx;
  }
  .title-1{
    font-size: 36upx;
    font-weight: bold;
    line-height: 96upx;
    height: 96upx;
    vertical-align: center;
    color: #484747;
  }
  .title-2{
    margin-top: 32upx;
    font-size: 34upx;
    font-weight: bold;
    line-height: 72upx;
    height: 72upx;
    vertical-align: center;
    color: #484747;
  }
  .desc{
    color: #9E9E9E;
    font-size: 26upx;
    padding-left: 10upx;
    font-weight: normal;
  }
  .title-desc{
    font-size: 28upx;
    color: #9E9E9E;
  }
  image{
    width: 750upx;
    padding-right: -20upx;
    padding-left: -20upx;
  }
  .flex-row{
    display: flex;
    flex-direction: row;
    margin-top: 30upx;
    justify-content: space-between;
    .box-item{
      width: 210upx;
      background-color: #F5F5F5;
      border-radius: 16upx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 10upx 5upx;
      .name{
        color: $config-color;
        font-size: 26upx;
        font-weight: bold;
      }
      .desc{
        font-size: 22upx;
        padding: 4upx 0;
        color: #9E9E9E;
      }
      .package-desc{
        font-size: 20upx;
        color: #9E9E9E;
      }
    }
  }
  .font-bold{
   font-weight: bold;
  }
}
