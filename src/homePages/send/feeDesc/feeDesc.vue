<script lang="ts">
import 'reflect-metadata';
import {Component, Prop, Vue} from 'vue-property-decorator';
import {ImageMixins} from "@/mixins/ImageMixins";
import config from "@/utils/config";

@Component({
  name: 'FeeDesc'
})
export default class FeeDesc extends ImageMixins {
  private BrandName=config.BrandName
  private boxList: any[] = [
    {name: '小格口', desc: '宽39cm高10cm', packageDesc: '适合文件,电子产品等'},
    {name: '中格口', desc: '宽39cm高20cm', packageDesc: '适合衣服，背包等'},
    {name: '大格口', desc: '宽39cm高37cm', packageDesc: '适合厚外套，棉被等'}
  ]

  goBack() {
    uni.navigateBack({delta: 1})
  }
}
</script>

<template src="./feeDesc.html"></template>
<style src="./feeDesc.scss" lang="scss" scoped></style>
