<view class="sendPackage">
    <view class="page-item">
        <!--订单状态-->
        <view class="status flex-row primary-color">
            <u-icon :color="iconColor" name="clock" size="42px"></u-icon>
            <view class="flex-column pl15" style="flex: 1">
                <view class="status-label">{{ sendStatus }}</view>
                <view class="cabinet-address primary-color">
                    {{ orderDesc }}
                    <span v-if="orderInfo.hasInbound !== 0">
                    <span v-if="orderInfo.sendStatus === 3|| orderInfo.sendStatus === 5"
                          style="font-weight: 600;color: #d10a0a">
                        取出物品后会自动退款
                    </span>
                    </span>
                </view>
                <view class="flex-row mt10">
                    <view v-if="orderInfo.sendStatus === 1" class="auth button-padding flex-row"
                          @click="scanQr(orderInfo.id)">
                        <u-icon color="#FFFFFF" name="scan" size="17px"></u-icon>
                        扫码开柜
                    </view>
                </view>
            </view>
        </view>
        <!--柜机信息-->
        <view class="cabinet flex-row">
            <view class="flex-column" style="flex: 1">
                <view class="flex-row pb10 align-center">
                    <view class="cabinet-name">{{ orderInfo.cabinetLocationName }}</view>
                    <view v-if="orderInfo.cabinetBoxType> -1" class="boxType">
                        {{ boxType }}
                    </view>
                </view>
                <view class="cabinet-address">
                    {{ orderInfo.cabinetLocationAddress }}
                </view>
                <view v-if="orderInfo.cabinetBoxLabel"
                      class="cabinet-address">
                    柜门号：{{ orderInfo.cabinetBoxLabel }}
                </view>
            </view>
            <view class="right"
                  @click="openLocation()">
                <view class="text-button">
                    导航
                </view>
            </view>
        </view>
        <!--运单信息-->
        <view class="cabinet flex-row waybill">
            <view class="flex-row align-center">
                <view class="flex-column" style="flex: 1">
                    <view class="cabinet-name">{{ brandName }}</view>
                    <view class="cabinet-address">
                        运单号: {{ waybillNo(orderInfo.waybillNo) }}
                    </view>
                </view>
                <view v-if="orderInfo.waybillNo !== '' && orderInfo.waybillNo !== null"
                      class="right"
                      @click="toTrackList(orderInfo.waybillNo)">
                    <view class="text-button">
                        查看物流
                    </view>
                </view>
            </view>
        </view>

        <!--地址信息-->
        <view class="address">
            <view class="cabinet-name">寄件信息</view>
            <view class="address-item">
                <img :src="getImage('send')" alt="">
                <view class="address-info">
                    <view class="name">
                        {{ getAddressUser(addressType.SENDER) }}
                    </view>
                    <view class="info">
                        {{ getAddressInfo(addressType.SENDER) }}
                    </view>
                </view>
            </view>
            <view class="border"></view>
            <view class="address-item">
                <img :src="getImage('receive')" alt="">
                <view class="address-info">
                    <view class="name">
                        {{ getAddressUser(addressType.RECEIVE) }}
                    </view>
                    <view class="info">
                        {{ getAddressInfo(addressType.RECEIVE) }}
                    </view>
                </view>
            </view>
        </view>

        <!--订单信息-->
        <view class="cabinet order-info">
            <view class="flex-column" style="flex: 1">
                <view class="cabinet-name">订单信息</view>
                <view class="cabinet-address">
                    订单金额: {{ orderFee }}
                </view>
                <view class="cabinet-address">
                    订单信息: {{ orderInfo.bizNo }}
                </view>
                <view class="cabinet-address">
                    下单时间: {{ orderInfo.createTime }}
                </view>
                <view v-if="orderInfo.sendStatus !== 1 && orderInfo.hasInbound === 1" class="cabinet-address">
                    投柜时间: {{ orderInfo.inboundTime }}
                </view>
                <view v-if="orderInfo.sendStatus === 3 || orderInfo.sendStatus === 5 || orderInfo.sendStatus === 6"
                      class="cabinet-address">
                    取消时间: {{ orderInfo.cancelTime }}
                </view>
                <view v-if="(orderInfo.sendStatus === 7) && orderInfo.hasInbound === 1"
                      class="cabinet-address">
                    客户取出时间: {{ orderInfo.outboundTime ? orderInfo.outboundTime : '' }}
                </view>
                <view v-if="(orderInfo.sendStatus === 4 || orderInfo.sendStatus === 9) && orderInfo.hasOutbound === 1"
                      class="cabinet-address">
                    快递员取出时间: {{ orderInfo.outboundTime ? orderInfo.outboundTime : '' }}
                </view>
                <view v-if="orderInfo.sendStatus === 9" class="cabinet-address">
                    订单完成: {{ orderInfo.updateTime ? orderInfo.updateTime : '' }}
                </view>
            </view>
        </view>

        <view v-if="orderInfo.sendStatus === 6 || orderInfo.sendStatus === 7" class="cabinet order-info"
              style="margin-bottom: 140upx">

            <view class="flex-column" style="flex: 1">
                <view class="cabinet-name">退款记录</view>
                <view v-for="(item, index) in refundList" :key="index">
                    <view class="refund-item">
                        <view class="common-info">
                            交易单号: {{ item.transactionId }}
                        </view>
                        <view class="common-info">
                            商户单号: {{ item.payTradeNo }}
                        </view>
                        <view class="common-info">
                            退款类型: {{ orderRefundType(item.type) }}
                        </view>
                        <view class="common-info">
                            退款申请时间: {{ item.applyTime }}
                        </view>
                        <view class="common-info">
                            退款申请说明: {{ item.applyDesc }}
                        </view>
                        <view class="common-info">
                            退款金额: {{ orderRefundFee(item.refundPrice) }}
                        </view>
                        <view class="common-info">
                            退款状态: {{ orderRefundStatus(item.refundStatus) }}
                        </view>
                    </view>
                    <view v-if="index !==refundList.length - 1" class="border"></view>
                </view>
            </view>
        </view>
    </view>
    <!--底部按钮-->
    <view v-if="orderInfo.sendStatus ==1 || orderInfo.sendStatus === 2 || orderInfo.sendStatus === 4 || orderInfo.sendStatus === 6"
          class="bottom">
        <view class="left">
            <text class="iconfont icon-bodadianhua" @click="mackPhoneCall"></text>
            <!--            <view class="price-title">-->
            <!--                预估-->
            <!--                <span class="price">￥{{ brandInfo.price }}</span>-->
            <!--            </view>-->
            <!--            <view class="content">-->
            <!--                以快递员确认定价为准-->
            <!--            </view>-->
        </view>
        <view class="right">
            <view v-if="orderInfo.sendStatus === 1 || orderInfo.sendStatus === 2" class="text-button"
                  @click="cancelOrder()">
                取消订单
            </view>
            <view v-if="orderInfo.sendStatus === 1" class="text-button ml30"
                  @click="editOrder()">
                修改订单
            </view>
            <view v-if="orderInfo.sendStatus === 4 || orderInfo.sendStatus === 6"
                  class="text-button text-button-long ml30"
                  @click="contactCourier()">
                联系快递员
            </view>
        </view>
    </view>
</view>
