.sendPackage {
  width: 750upx;
  display: flex;
  position: relative;
  flex-direction: column;
  height: 100vh;

  .page-item {
    flex: 1;
    width: 750upx;
    overflow-y: auto;
    //margin-bottom: 118upx;
  }

  //柜机信息
  .status {
    width: 690upx;
    margin-left: 30upx;
    margin-top: 30upx;
    max-height: 320upx;
    background: #FFFFFF;
    border-radius: 10upx;
    display: flex;
    align-items: flex-start;
    padding: 20upx;
    box-sizing: border-box;

    .status-label {
      font-size: 32upx;
      font-weight: bold;
    }
  }

  //柜机信息
  .cabinet {
    width: 690upx;
    min-height: 140upx;
    margin-left: 30upx;
    margin-top: 30upx;
    background: #FFFFFF;
    border-radius: 10upx;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 20upx;
  }

  .cabinet-name {
    font-size: 32upx;
    font-weight: 400;
  }

  .boxType {
    background-color: $config-color;
    padding: 2upx 14upx;
    border-radius: 10upx;
    display: flex;
    height: 36upx;
    align-items: center;
    font-size: 22upx;
    font-weight: 400;
    color: #ffffff;
    margin-left: 10upx;
  }

  .cabinet-address {
    margin-top: 6upx;
    font-size: 26upx;
    overflow: hidden;
    width: 510upx;
    white-space: pre-wrap;
    text-overflow: ellipsis;
  }
  .common-info {
    margin-top: 6upx;
    font-size: 26upx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .border {
    width: 630upx;
    margin: 10upx 0;
    height: 1upx;
    background-color: #EEEEED;
  }
  .right-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;
    font-size: 24upx;
    width: 120upx;
    height: 72upx;
    border-radius: 10upx;
    background-color: $config-color;
  }

  //地址信息
  .address {
    width: 690upx;
    margin-left: 30upx;
    margin-top: 30upx;
    height: 360upx;
    background-color: #FFFFFF;
    border-radius: 10upx;
    display: flex;
    flex-direction: column;
    padding: 20upx;
    box-sizing: border-box;

    .address-item {
      flex: 1;
      display: flex;
      flex-direction: row;
      align-items: center;

      image {
        width: 50upx;
        height: 50upx;
      }

      .address-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-left: 20upx;

        .name {
          font-size: 28upx;
          font-weight: 400;
          color: #000000;
          max-width: 400upx;
          flex-wrap: wrap;
        }

        .info {
          margin-top: 8upx;
          font-size: 22upx;
          font-weight: 400;
          color: #999999;
          word-wrap: normal;
          white-space: pre-wrap;
          overflow: hidden;
          max-width: 490upx;
          text-overflow: ellipsis;
        }
      }
    }

    .border {
      width: 630upx;
      height: 1upx;
      background-color: #EEEEED;
    }
  }

  .waybill {
    height: 120upx;
  }


  .company {
    width: 690upx;
    height: 118upx;
    margin-left: 30upx;
    margin-top: 30upx;
    background: #FFFFFF;
    border-radius: 10upx;
    display: flex;
    box-sizing: border-box;
    flex-direction: column;
    padding: 18upx 20upx 0 20upx;
  }

  //订单信息
  .package {
    width: 690upx;
    height: 403upx;
    margin-left: 30upx;
    margin-top: 30upx;
    background: #FFFFFF;
    border-radius: 10upx;
    display: flex;
    box-sizing: border-box;
    flex-direction: column;
    padding: 18upx 20upx 0 20upx;
  }

  .space-two {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 91upx;
    margin-left: 30upx;
    margin-right: 30upx;

    .title {
      font-size: 28upx;
      font-weight: 400;
      color: #000000;
    }

    .right {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;

      .box {
        height: 34upx;
        border: 1upx solid $config-color;
        border-radius: 30upx;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1upx 12upx;
        margin-right: 20upx;

        text {
          font-size: 18upx;
          font-weight: 400;
          color: $config-color;
        }
      }

      .icon {
        margin-left: 20upx;
        font-size: 28upx;
        color: black;
      }
    }
  }

  .space-one-start {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    min-height: 91upx;
    margin-left: 30upx;
    margin-right: 30upx;
  }

  .remarks-bg {
    width: 610upx;
    height: 180upx;
    margin-left: 30upx;
    margin-right: 30upx;
    background: #F5F5F5;
    border-radius: 10upx;

    ::v-deep.u-textarea {
      background-color: transparent !important;

      .u-textarea__count {
        background-color: transparent !important;
      }
    }
  }

  .bottom-width {
    border-bottom: 1px solid #EEEEED;
  }

  .order-info {
    min-height: 200upx;
  }

  //底部按钮
  .bottom {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: row;
    height: 118upx;
    width: 750upx;
    background-color: #ffffff;
    padding: 10upx 30upx;
    box-sizing: border-box;

    .left {
      display: flex;
      flex-direction: column;
      flex: 1;
      justify-content: center;

      text {
        font-size: 48upx;
        color: $config-color;
      }

      .price-title {
        font-size: 30upx;
        font-weight: 400;
        color: #000000;
      }

      .price {
        color: $config-color;
      }

      .content {
        font-size: 22upx;
        font-weight: 400;
        color: #999999;
      }
    }

  }

  .right {
    display: flex;
    flex-direction: row;
    align-items: center;

    .text-button {
      height: 42upx;
      width: 120upx;
      background-color: #ffffff;
      border: 1px solid #E5E5E5;
      border-radius: 10upx;
      padding: 8upx 12upx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28upx;
    }

    .text-button-long {
      width: 140upx;
    }

    .active {
      background: $config-color;
    }
  }

  .color-active {
    color:  $config-color;
  }
}
