<script lang='ts'>
import 'reflect-metadata';
import {Component, Mixins, Watch} from 'vue-property-decorator';
import {ImageMixins} from '@/mixins/ImageMixins';
import {cabinetForm} from '@/utils/types';
import InputWidget from '@/components/input-widget/index.vue';
import Config from '@/utils/config';
import config from '@/utils/config';
import {Modal} from '@/utils';
import {getCabinetInfo} from '@/model/cabinet';
import {cancelOrder, getOrderDetail, refundList} from '@/model/send';
import {PackageMixins} from '@/mixins/packageMixin';

// 地址类型
export enum AddressType {
  SENDER = 'sender',
  RECEIVE = 'receive'
}

@Component({
  name: 'orderDetail',
  components: {InputWidget}
})
export default class SendForm extends Mixins(ImageMixins, PackageMixins) {
  private iconColor=''
  private orderInfo: any = {
    'id': '',
    'createBy': '',
    'createTime': '',
    'updateBy': '',
    'updateTime': '',
    'orderType': -1,
    'orderStatus': 1,
    'orderNo': '',
    'bizNo': '',
    'channelId': '',
    'channelName': '',
    'siteId': '',
    'siteName': '',
    'shopId': '',
    'shopName': '',
    'cabinetLocationId': '',
    'cabinetLocationCode': '',
    'cabinetLocationName': '',
    'cabinetLocationAddress': '',
    'cabinetId': '',
    'cabinetCode': '',
    'cabinetName': '',
    'cabinetSerialNo': 0,
    'cabinetBoxId': '',
    'cabinetBoxType': -1,
    'cabinetBoxLabel': '',
    'cabinetBoxPcbNo': '',
    'courierMobile': '',
    'courierName': '',
    'courierId': '',
    'shelfId': '',
    'shelfName': '',
    'storeType': 1,
    'receiverName': '',
    'receiverMobile': '',
    'receiverMobileLast4': '',
    'checkCode': '',
    'messageType': 0,
    'messageSmsTplId': '',
    'messageSmsStatus': 0,
    'messageSmsDeliveryTime': '',
    'messageSmsReceiptTime': '',
    'messageSmsReceiptContent': '',
    'messageWxTplId': '',
    'messageWxStatus': 0,
    'messageWxDeliveryTime': '',
    'messageWxReceiptTime': '',
    'messageWxReceiptContent': '',
    'inboundUserId': '0',
    'inboundUserName': '',
    'inboundUserFee': 0,
    'inboundUserMobile': '',
    'inboundTime': '',
    'inboundYm': '',
    'inboundYmd': '',
    'inboundImageUrl': '',
    'hasOutbound': 0,
    'hasInbound': 0,
    'outboundType': '',
    'outboundUserId': '',
    'outboundUserName': '',
    'outboundUserFee': 0,
    'outboundYm': '',
    'outboundYmd': '',
    'outboundImageUrl': '',
    'remarks': '',
    'serviceDuration': 0,
    'version': 0,
    'sendStatus': 9,
    'waybillNo': '',
    'waybillNoLast6': '',
    'brandCode': '',
    'brandName': '',
    'senderName': '',
    'senderIdNumber': '',
    'senderMobile': '',
    'senderProvinceCode': '',
    'senderProvinceName': '',
    'senderCityCode': '',
    'senderCityName': '',
    'senderAreaCode': '',
    'senderAreaName': '',
    'senderStreetCode': '',
    'outboundTime': '',
    'senderStreetName': '',
    'senderAddress': '',
    'receiverProvinceCode': '',
    'receiverProvinceName': '',
    'receiverCityCode': '',
    'receiverCityName': '',
    'receiverAreaCode': '',
    'receiverAreaName': '',
    'receiverStreetCode': '',
    'receiverStreetName': '',
    'receiverAddress': '',
    'cancelReason': '',
    'cancelTime': ''
  }
  private locationAuth: boolean = false
  private cabinet: cabinetForm = new cabinetForm()
  private addressType: any = AddressType
  private refundList: any[] = []
  private brandInfo: any = {
    brandCode: '',
    boxType: -1,
    price: 0
  }
  private otherInfo: any = {
    isRealName: 1,
    remarks: ''
  }

  @Watch('cabinetBoxIsOpen')
  getCabinetBoxIsOpen(val: string, oldVal: string) {
    if (val) {
      this.getOrderInfo(this.orderInfo.id)
      this.resetBox()
    }
  }

  get sendStatus() {
    return Config.ORDER_STATUS[this.orderInfo.sendStatus]
  }

  get orderDesc() {
    if (this.orderInfo.sendStatus === 1) { // 包裹待投柜
      return '订单已创建，请携带物品至指定柜机，扫描柜机的寄件二维码，柜门打开后放入物品并关闭门，快递员接到订单后会取走物品并为您寄送包裹'
    }
    if (this.orderInfo.sendStatus === 2) {// 快递员待取出
      return '已接到您的订单,快递员正在快马加鞭的赶来,快递员取走包裹后，会为您生成快递单号'
    }
    if (this.orderInfo.sendStatus === 3) {
      if (this.orderInfo.hasInbound === 0) {
        return '订单已取消'
      }
      return `取件码：${this.orderInfo.checkCode} \r\n请在柜机中点击：我要取件，并输入取件码，柜门打开后取走物品即可，`
    }
    if (this.orderInfo.sendStatus === 4) {
      return `快递员已取走您的包裹，稍后为您生成运单号`
    }
    if (this.orderInfo.sendStatus === 5) {
      return `取件码：${this.orderInfo.checkCode} \r\n请在柜机中点击：我要取件，并输入取件码，柜门打开后取走物品即可，`
    }
    if (this.orderInfo.sendStatus === 6) {
      return `订单由快递员取消，请沟通快递员取回物品`
    }
    if (this.orderInfo.sendStatus === 7) {
      return `包裹已取出`
    }
    if (this.orderInfo.sendStatus === 9) {
      return `订单已寄出,感谢您的使用！`
    }
    return ''
  }

  get brandName() {
    return Config.EXPRESS[this.orderInfo.brandCode]
  }

  get orderFee() {
    return '￥' + (this.orderInfo.inboundUserFee / 1000).toFixed(2)
  }

  async openLocation() {
    uni.openLocation({
      longitude: Number(this.cabinet.longitude),
      latitude: Number(this.cabinet.latitude)
    })
  }


  async getCabinetInfo() {
    if (this.orderInfo.cabinetLocationCode) {
      var res = await getCabinetInfo({code: this.orderInfo.cabinetLocationCode})
      if (res) {
        this.cabinet = res
      }
    }
  }

  getAddressUser(type: AddressType) {
    let label = ''
    if (type === AddressType.SENDER) {
      label = '寄件人信息'
      if (this.orderInfo.senderName !== '' && this.orderInfo.senderName != '') {
        label = this.orderInfo.senderName + '\u3000' + this.orderInfo.senderMobile
      }
    } else if (type === AddressType.RECEIVE) {
      label = '收件人信息'
      if (this.orderInfo.receiverName !== '' && this.orderInfo.receiverName != '') {
        label = this.orderInfo.receiverName + '\u3000' + this.orderInfo.receiverMobile
      }
    }
    return label
  }


  get boxType() {
    if (this.orderInfo.cabinetBoxType > -1) {
      return Config.BoxLabel.get(this.orderInfo.cabinetBoxType) + '格'
    } else {
      return ''
    }
  }

  openBrandPage() {
    uni.navigateTo({url: '/homePages/send/brandSelect/brandSelect?cabinetInfo=' + encodeURIComponent(JSON.stringify(this.cabinet))})
  }

  getAddressInfo(type: AddressType) {
    let label = ''
    if (type === AddressType.SENDER) {
      label = '点击填写寄件人信息'
      if (this.orderInfo.senderAddress !== '') {
        label = this.getAddress(this.orderInfo.senderProvinceName, this.orderInfo.senderCityName, this.orderInfo.senderAreaName, this.orderInfo.senderStreetName, this.orderInfo.senderAddress)
      }
    } else if (type === AddressType.RECEIVE) {
      label = '点击填写收件人信息'
      if (this.orderInfo.receiverAddress !== '') {
        label = this.getAddress(this.orderInfo.receiverProvinceName, this.orderInfo.receiverCityName, this.orderInfo.receiverAreaName, this.orderInfo.receiverStreetName, this.orderInfo.receiverAddress)
      }
    }
    return label
  }


  geIsRealName() {
    let label = '已实名'
    if (this.otherInfo.isRealName === 0) {
      label = '未实名'
    }
    return label
  }

  async cancelOrder() {
    let msg = '是否确认取消该订单？'
    // if (this.orderInfo.sendStatus === 2) {
    //   msg = `请通过取件码:${this.orderInfo.checkCode},在柜机前取走包裹`
    // }
    await Modal.confirm(msg, '取消订单')
    const res = await cancelOrder({orderId: this.orderInfo.id})
    if (res) {
      this.orderInfo = res
    }
  }

  editOrder() {
    uni.navigateTo({url: '/homePages/send/sendForm/sendForm?orderInfo=' + encodeURIComponent(JSON.stringify(this.orderInfo)) + '&&cabinetInfo=' + encodeURIComponent(JSON.stringify(this.cabinet))})
  }

  contactCourier() {
    if (this.orderInfo.courierMobile) {
      uni.makePhoneCall({
        phoneNumber: this.orderInfo.courierMobile
      })
    } else {
      Modal.toast('未找到联系人电话')
    }
  }

  mackPhoneCall() {
    if (this.cabinet.mobile) {
      uni.makePhoneCall({
        phoneNumber: this.cabinet.mobile
      })
    } else {
      Modal.toast('未找到联系人电话')
    }
  }

  onUnload(event: any) {
    uni.switchTab({url: '/pages/search/index/index'})
  }

  onShow() {
    let color=config.CONFIG_COLOR
    this.iconColor=color
    const that = this
    uni.authorize({
      scope: 'scope.userLocation',
      success() {
        that.locationAuth = true
      },
      fail() {
        that.locationAuth = false
      }
    })
  }

  async getRefundList() {
    const res = await refundList({orderId: this.orderInfo.id})
    if (res) {
      this.refundList = res

    }
  }

  async getOrderInfo(id: string) {
    const res = await getOrderDetail({orderId: id})
    if (res) {
      this.orderInfo = res
      // this.orderInfo.sendStatus = 1
      // this.orderInfo.hasOutbound = 1
      if (this.orderInfo) {
        await this.getCabinetInfo()
      }
      if (this.orderInfo.sendStatus === 6 || this.orderInfo.sendStatus === 7) {
        await this.getRefundList()
      }
    }
  }

  onLoad(e: any) {
    if (e.orderId) {
      this.getOrderInfo(e.orderId)
    }
  }
}
</script>

<template src='./orderDetail.html'></template>
<style src='./orderDetail.scss' lang='scss' scoped></style>
