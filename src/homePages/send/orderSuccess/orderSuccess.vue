<script lang="ts">
import 'reflect-metadata';
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import {PackageMixins} from "@/mixins/packageMixin";

@Component({
  name: 'OrderSuccess'
})
export default class OrderSuccess extends PackageMixins {
  private orderId: string = ''

  @Watch('cabinetBoxIsOpen')
  getCabinetBoxIsOpen(val: string, oldVal: string) {
    if (val) {
      this.resetBox()
      this.toOrderDetail()
    }
  }
  toOrderDetail() {
    uni.navigateTo({url: `/homePages/send/orderDetail/orderDetail?orderId=` + this.orderId})
  }

  scanOpenBox() {
  }
  onUnload(event: any) {
    uni.switchTab({url: '/pages/search/index/index'})
  }
  onLoad(e: any) {
    if (e.orderId) {
      this.orderId = e.orderId
    }
  }

}
</script>

<template src="./orderSuccess.html"></template>
<style src="./orderSuccess.scss" lang="scss" scoped></style>
