<view>
    <view class="container">
        <view class="bar">
            <view class="title">
                您有<span class="count">{{ packageList.length }}</span>件待投柜寄件包裹订单
            </view>
        </view>
        <scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore">
            <view v-if="packageList.length>0" class="scroll-view-list">
                <view v-for="(item, index) in packageList" :key="index">
                    <view class="scroll-item">
                        <view @click="toPackageDetail(item)">
                            <view class="space-two align-center">
                                <view class="flex-row align-center">
                                    <view class="cabinet-name">{{ item.brandName }}</view>
                                    <view class="label pl15">{{ waybillNo(item.waybillNo) }}</view>
                                </view>
                                <view class="flex-row align-center">
                                    <u-icon name="clock" :color="iconColor" size="16px"></u-icon>
                                    <text class="primary-color">{{ orderStatus(item.orderStatus) }}</text>
                                </view>
                            </view>
                            <view style="padding: 12upx"></view>
                            <view class="address-item">
                                <img :src="getImage('send')" alt="">
                                <view class="address-info">
                                    <view class="name">
                                        {{ getAddressUser(item.senderName, item.senderMobile) }}
                                    </view>
                                    <view class="info">
                                        {{
                                            getAddress(item.senderProvinceName, item.senderCityName, item.senderAreaName, item.senderStreetName, item.senderAddress)
                                        }}
                                    </view>
                                </view>
                            </view>
                            <view class="border"></view>
                            <view class="address-item">
                                <img :src="getImage('receive')" alt="">
                                <view class="address-info">
                                    <view class="name">
                                        {{ getAddressUser(item.receiverName, item.receiverMobile) }}
                                    </view>
                                    <view class="info">
                                        {{
                                            getAddress(item.receiverProvinceName, item.receiverCityName, item.receiverAreaName, item.receiverStreetName, item.receiverAddress)
                                        }}
                                    </view>
                                </view>
                            </view>
                            <view style="padding: 2upx"></view>
                        </view>
                        <view class="space-two flex-row">
                            <view style="flex: 1;background-color: transparent;color: transparent;width: 100%;height: 100%"
                                  @click="toPackageDetail(item)">
                                查看详情
                            </view>
                            <view>
                                <view class="auth button-padding flex-row" v-if="item.orderStatus === 1"
                                      @click="scanQr(item.id)">
                                    <u-icon name="scan" color="#FFFFFF" size="17px"></u-icon>
                                    扫码开柜
                                </view>
                                <view class="auth button-padding flex-row" v-if="item.orderStatus === 9"
                                      @click="toTrackList(item.waybillNo)">
                                    查看物流
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <u-loadmore :status="loadStatus" margin-top="20" bg-color="#f6f6f6" font-size="13" />
                <view class="padding-30"></view>
            </view>
            <view class="no-result" v-else>
                <text class="text">暂无数据哦~</text>
            </view>
        </scroll-view>
        <view class="bottom">
            <button class="btn auth"
                    @click="addNewSendOrder">
                <text>我要寄件</text>
            </button>
        </view>
    </view>
</view>
