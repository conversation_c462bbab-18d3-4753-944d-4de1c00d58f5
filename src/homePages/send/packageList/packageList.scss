.container{
  width: 750upx;
  position: relative;
  height: 100vh;
  .bar{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 86upx;
    width: 750upx;
  }
  .title {
    font-size: 28upx;
    font-weight: 400;
    color: #333333;
  }
  .count{
    font-size: 32upx;
    font-weight: 400;
    color: $config-color;
  }
  .scroll-view{
    position: absolute;
    top: 80upx;
    left: 0;
    right: 0;
    bottom: 120upx;
    .scroll-view-list {
      overflow: auto;
      margin: auto;
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
    }
    .item{
      width: 690upx;
      margin-left: 30upx;
      //height: 325upx;
      background: #FFFFFF;
      margin-bottom: 20upx;
      border-radius: 10upx;
      padding:  24upx 30upx;
      box-sizing: border-box;
    }
    .scroll-item{
      width: 690upx;
      margin-left: 30upx;
      max-height: 448upx;
      background: #FFFFFF;
      margin-bottom: 20upx;
      border-radius: 10upx;
      padding:  24upx 30upx;
      box-sizing: border-box;
    }
    .address-item {
      flex: 1;
      display: flex;
      flex-direction: row;
      align-items: center;

      image {
        width: 50upx;
        height: 50upx;
      }

      .address-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-left: 20upx;

        .name {
          font-size: 28upx;
          font-weight: 400;
          color: #000000;
          max-width: 400upx;
          flex-wrap: wrap;
        }

        .info {
          margin-top: 3upx;
          font-size: 22upx;
          font-weight: 400;
          color: #999999;
          overflow: hidden;
          max-width: 490upx;
          text-overflow: ellipsis;
          word-wrap: normal;
          white-space: pre-wrap;
        }
      }

    }

    .border {
      width: 630upx;
      height: 1upx;
      margin: 12upx 0;
      background-color: #EEEEED;
    }
  }
  .check-code{
    margin-left: 20upx;
    font-size: 36upx;
    font-weight: 500;
    color: $config-color;
  }
  .label{
    font-size: 28upx;
    font-weight: 400;
    flex: 1;
    color: #585858;
  }

  .mini-label{
    font-size: 24upx;
    vertical-align: middle;
    font-weight: 400;
    color: #999999;
  }
  .cabinet-name{
    font-size: 30upx;
    font-weight: 500;
    max-width: 345upx;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    color: #333333;
  }
  .space-two {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  .space-one-end {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
  }
  .space-one-start {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }
  .item-row{
    image{
      width: 162upx;
      height: 162upx;
    }
  }
  .fee-un-pay{
    font-size: 28upx;
    font-weight: 400;
    color: $config-color;
  }
  .padding-30 {
    height: 30px;
  }
}
