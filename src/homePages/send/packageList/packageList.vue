<script lang="ts">
import 'reflect-metadata';
import {Component, Mixins} from 'vue-property-decorator';
import {cabinetCodeDto, outboundDto, payDto, waybillInfo} from "@/utils/types";
import Modal from "@/utils/core/modal";
import {getCabinetInfo, getSendOrderList, outboundOrder} from "@/model/cabinet";
import {getOrderIsPay, getPayParams} from "@/model/pay";
import {ImageMixins} from "@/mixins/ImageMixins";
import {UserMixins} from "@/mixins/userMixin";
import config from "@/utils/config";

@Component({
  name: 'packageList'
})
export default class PackageList extends Mixins(ImageMixins, UserMixins) {
  private iconColor=''
  private cabinetLocationCode: string = ''
  private cabinetLocationId: string = ''
  private packageList: waybillInfo[] = []
  private current: number = 1
  private total: number = 0
  private loadStatus: string = 'noMore'

  async pick(item: waybillInfo) {
    const money_yuan: number = item.price / 1000
    const that = this
    if (item.price > 0) {
      let params: outboundDto = {
        orderId: item.orderId,
        cabinetLocationCode: item.cabinetLocationCode,
      }
      /// 是否已付费,true:已付费,false:未付费
      const result = await getOrderIsPay(params)
      if (!result) {
        await Modal.confirm(`您的待取包裹超时需要付费的订单，共计${money_yuan}元`, '取出提示', '暂不取出', '支付取出')
        let info: payDto = {
          orderId: item.orderId,
          cabinetLocationCode: item.cabinetLocationCode,
        }
        const res = await getPayParams(info)
        wx.requestPayment({
          timeStamp: res.timeStamp,
          nonceStr: res.nonceStr,
          package: res.packageValue,
          signType: res.signType,
          paySign: res.paySign,
          success: async function () {
            await that.outboundPackage(item)
          },
          fail: function () {
          }
        })
      } else {
        await this.outboundPackage(item)
      }
    } else {
      await this.outboundPackage(item)
    }


  }

  async outboundPackage(item: waybillInfo) {
    await Modal.confirm(`确认取出${item.cabinetBoxLabel}号格口的包裹吗？\r\n注意开门`, '取出提示',)
    let info: outboundDto = {
      cabinetLocationCode: item.cabinetLocationCode,
      orderId: item.orderId
    }
    const res = await outboundOrder(info)
    if (res) {
      await Modal.confirm('柜门打开成功,请取件后关门', '关门提示', '取消', '我已关门')
      await this.getList(true)
    }
  }

  backHome() {
    uni.switchTab({url: '/pages/home/<USER>/index'})
  }

  loadMore() {
    if (this.packageList.length < this.total) {
      if (this.loadStatus === 'loading') {
        return
      }
      this.loadStatus = 'loading'
      this.current++
      this.getList()
    }
  }

  getKeepTime(time: string) {
    let hours: number = Math.ceil((new Date().getTime() - new Date(time.replace(/-/g, '/')).getTime()) / 1000 / 3600)
    return hours >= 24 ? Math.floor(hours / 24) + '天' + hours % 24 : hours
  }

  async getList(isGetCount = false) {
    let info: cabinetCodeDto = {}
    if (this.cabinetLocationId) {
      info = {
        ...info,
        cabinetId: this.cabinetLocationId
      }
    }
    if (this.cabinetLocationCode) {
      info = {
        ...info,
        cabinetLocationCode: this.cabinetLocationCode
      }
    }
    const res = await getSendOrderList(info)
    if (res) {
      this.packageList = res
      if (isGetCount) {
        if (this.packageList.length === 0) {
          uni.navigateTo({url: '/homePages/receive/allPick/allPick'})
        }
      }
    }
  }

  async addNewSendOrder() {
    let cabinet = {}
    if (this.cabinetLocationCode) {
      var res = await getCabinetInfo({code: this.cabinetLocationCode})
      if (res) {
        cabinet = res
      }
    }
    if (this.isRealName) {
      uni.navigateTo({url: '/homePages/send/sendForm/sendForm?cabinetInfo=' + encodeURIComponent(JSON.stringify(cabinet))})
    } else {
      uni.navigateTo({url: '/myPages/realNameAuth/realNameAuth?cabinetInfo=' + encodeURIComponent(JSON.stringify(cabinet))})
    }
  }

  onShow() {
    let color=config.CONFIG_COLOR
    this.iconColor=color
    this.getList()
  }

  onLoad(e: any) {
    this.getUserInfo()
    try {
      const info = JSON.parse(decodeURIComponent(e.info))
      this.cabinetLocationCode = info.cabinetLocationCode
      this.cabinetLocationId = info.cabinetLocationId
    } catch (e) {
      console.log(e)
    }
  }

  resetCurrent() {
    this.current = 1
  }

  async onPullDownRefresh() {
    this.resetCurrent()
    await this.getList()
    uni.stopPullDownRefresh()
  }
}
</script>

<template src="./packageList.html"></template>
<style src="./packageList.scss" lang="scss" scoped></style>
