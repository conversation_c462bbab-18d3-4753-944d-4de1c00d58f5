<view class="sendPackage">
    <view class="page-item">
        <!--柜机信息-->
        <view class="cabinet">
            <view class="cabinet-loc" @click="goCabinetLocationPage">
                <img class="loc" :src="getImage('location')" alt="">
                <view class="name" style="flex: 1">
                    已选柜机：
                    <span> {{ cabinet.name }}</span>
                </view>
                <view class="icon-btn2">
                    <text class="iconfont icon-you icon"></text>
                </view>
            </view>
        </view>
        <!--地址信息-->
        <view class="address">
            <view class="address-item" @click="goAddressEdit('send')">
                <img :src="getImage('send')" alt="">
                <view class="address-info">
                    <view class="name">
                        {{ getAddressUser(addressType.SENDER) }}
                    </view>
                    <view class="info">
                        {{ getAddressInfo(addressType.SENDER) }}
                    </view>
                </view>
                <view class="address-book" @click.capture="goAddressList('send')">
                    <img :src="getImage('book')" alt="">
                </view>
            </view>
            <view class="border"></view>
            <view class="address-item" @click="goAddressEdit('receive')">
                <img :src="getImage('receive')" alt="">
                <view class="address-info">
                    <view class="name">
                        {{ getAddressUser(addressType.RECEIVE) }}
                    </view>
                    <view class="info">
                        {{ getAddressInfo(addressType.RECEIVE) }}
                    </view>
                </view>
                <view class="address-book" @click.capture="goAddressList('receive')">
                    <img :src="getImage('book')" alt="">
                </view>
            </view>
        </view>

        <view class="company">
            <view class="space-two">
                <text class="title">快递公司</text>
                <view class="right" @click="openBrandPage()">
                    <text class="title">{{ getBrandInfo() }}</text>
                    <view class="boxType" v-if="brandInfo.boxType> 0">
                        <text>{{ getBoxType() }}</text>
                    </view>
                    <text class="iconfont icon-you icon"></text>
                </view>
            </view>
        </view>
        <!--订单信息-->
        <!--        <view class="package">-->
        <!--            <view class="space-two bottom-width">-->
        <!--                <text class="title">支付方式</text>-->
        <!--                <view class="right" @click="openPop('payMethod')">-->
        <!--                    <text class="title">{{ getPayMethod() }}</text>-->
        <!--                    <text class="iconfont icon-you icon"></text>-->
        <!--                </view>-->
        <!--            </view>-->
        <!--            <view class="space-two bottom-width">-->
        <!--                <text class="title">快递服务</text>-->
        <!--                <view class="right" @click="openPop('cabinetSelect')">-->
        <!--                    <view class="box">-->
        <!--                        <text> {{ getCabinetBoxLabel() }}</text>-->
        <!--                    </view>-->
        <!--                    <text class="title">{{ getBrandName() }}</text>-->
        <!--                    <text class="iconfont icon-you icon"></text>-->
        <!--                </view>-->
        <!--            </view>-->
        <!--            <view class="space-two">-->
        <!--                <text class="title">物品类型</text>-->
        <!--                <view class="right" @click="openPop('packageType')">-->
        <!--                    <text class="title">{{ getPackageType() }}</text>-->
        <!--                    <text class="iconfont icon-you icon"></text>-->
        <!--                </view>-->
        <!--            </view>-->
        <!--        </view>-->
        <!--其他信息-->
        <view class="real-name">
            <!--            <view class="space-two bottom-width">-->
            <!--                <text class="title">实名认证</text>-->
            <!--                <view class="right">-->
            <!--                    <text class="title">{{ geIsRealName() }}</text>-->
            <!--                    <text class="iconfont icon-you icon"></text>-->
            <!--                </view>-->
            <!--            </view>-->
            <view class="space-one-start">
                <text class="title">备注</text>
            </view>
            <view class="remarks-bg">
                <u-textarea ref="textarea" :formatter="formatter" v-model="otherInfo.remarks" placeholder="如有备注请填于此处，最多输入20个字（建议填写物品类型）" count :maxlength="20"></u-textarea>
            </view>
        </view>
        <!--授权-->
        <view class="auth2" @click="agreeAuth">
            <text class="iconfont icons" :class="isAgree ? 'icon-xuanze05 al': 'icon-xuanze04'"></text>
            我已阅读并同意
            <span class="color-active" @click.stop="goDoc()">《智能柜寄件服务协议》</span>
        </view>

        <!--支付方式选择弹窗-->
        <!--        <u-popup :show="showBottomSelect" mode="bottom" round="20" @close="this.showBottomSelect = false">-->
        <!--            <view class="pop">-->
        <!--                <view class="pop1" v-if="popType ==='payMethod'">-->
        <!--                    <view class="list">-->
        <!--                        <view class="item" :class="{'active-bor': packageInfo.payMethod === 1}"-->
        <!--                              @click="packageInfo.payMethod = 1">-->
        <!--                            <img :src="getImage('payOnline')" alt="">-->
        <!--                            <text class="title mt15" :class="{'active': packageInfo.payMethod === 1}">到柜支付</text>-->
        <!--                            <text class="sub-title mt10" :class="{'active': packageInfo.payMethod === 1}">在快递柜前付款</text>-->
        <!--                        </view>-->
        <!--                        <view class="item" :class="{'active-bor': packageInfo.payMethod === 2}"-->
        <!--                              @click="packageInfo.payMethod = 2">-->
        <!--                            <img :src="getImage('payOnline')" alt="">-->
        <!--                            <text class="title mt10" :class="{'active': packageInfo.payMethod === 2}">先寄后付</text>-->
        <!--                            <text class="content">微信支付分</text>-->
        <!--                            <text class="sub-title mt10" :class="{'active': packageInfo.payMethod === 2}">快递员收件后扣费-->
        <!--                            </text>-->
        <!--                        </view>-->
        <!--                    </view>-->
        <!--                    <button class="btn primary"-->
        <!--                            @click="this.showBottomSelect = false">-->
        <!--                        <text>确定</text>-->
        <!--                    </button>-->
        <!--                </view>-->
        <!--                <view class="pop2" v-if="popType ==='cabinetSelect'">-->
        <!--                    <view class="center">-->
        <!--                        选择快递-->
        <!--                    </view>-->
        <!--                    <view class="middle">-->
        <!--                        <view class="item">-->
        <!--                            <scroll-view scroll-y class="scroll-view">-->
        <!--                                <view v-for="(item, index) in brandList" :key="index" class="brand-item"-->
        <!--                                      :class="{'active-select': packageInfo.brand ===item.brandCode}"-->
        <!--                                      @click="this.packageInfo.brand = item.brandCode">-->
        <!--                                    <img :src="item.brandLogo" alt="">-->
        <!--                                    <text>{{ item.brandName }}</text>-->
        <!--                                </view>-->
        <!--                            </scroll-view>-->
        <!--                        </view>-->
        <!--                        <view class="item">-->
        <!--                            <scroll-view scroll-y class="scroll-view">-->
        <!--                                <view v-for="(item_, index) in cabinetBoxList" :key="index" class="brand-item"-->
        <!--                                      :class="{'active-select': packageInfo.cabinetBoxType === item_.boxType}"-->
        <!--                                      @click="setCabinetBoxType(item_)">-->
        <!--                                    <text class="no-margin">{{ item_.boxName }}</text>-->
        <!--                                    <text>￥{{ (item_.price / 1000).toFixed(2) }}</text>-->
        <!--                                </view>-->
        <!--                            </scroll-view>-->
        <!--                        </view>-->
        <!--                    </view>-->
        <!--                    <button class="btn primary"-->
        <!--                            @click="this.showBottomSelect = false">-->
        <!--                        <text>确定</text>-->
        <!--                    </button>-->
        <!--                </view>-->
        <!--                <view class="pop3" v-if="popType ==='packageType'">-->
        <!--                    <view class="center">-->
        <!--                        物品类型-->
        <!--                    </view>-->
        <!--                    <view class="center-item">-->
        <!--                        <view class="package-list">-->
        <!--                            <view class="package-item" v-for="(item, index) in packageTypeList" :key="index"-->
        <!--                                  :class="{'active-package-item': packageInfo.packageType === item.value}"-->
        <!--                                  @click="packageInfo.packageType = item.value">-->
        <!--                                <text>{{ item.name }}</text>-->
        <!--                            </view>-->
        <!--                        </view>-->
        <!--                    </view>-->
        <!--                    <view class="center">-->
        <!--                        预估重量-->
        <!--                    </view>-->
        <!--                    <view class="weight-button">-->
        <!--                        <view class="num-btn" @click="packageInfo.packageWeight&#45;&#45;">－</view>-->
        <!--                        <view class="amount-view">-->
        <!--                            <text>{{ packageInfo.packageWeight }}-->
        <!--                                <text class="weight-flag">KG</text>-->
        <!--                            </text>-->
        <!--                        </view>-->
        <!--                        <view class="num-btn" @click="packageInfo.packageWeight++">＋</view>-->
        <!--                    </view>-->
        <!--                    <button class="btn primary"-->
        <!--                            @click="this.showBottomSelect = false">-->
        <!--                        <text>确定</text>-->
        <!--                    </button>-->
        <!--                </view>-->
        <!--            </view>-->
        <!--        </u-popup>-->
    </view>
    <!--底部按钮-->
    <view class="bottom">
        <view class="left">
            <view class="price-title">
                预估
                <span class="price">￥{{ brandInfo.price }}</span>
            </view>
            <view class="content">
                以快递员确认定价为准
            </view>
        </view>
        <view class="right">
            <button class="button" :class="{'active': canSave}" @click="checkOrder">
                <text>{{ isEdit ? '保存' : '立即下单' }}</text>
            </button>
        </view>
    </view>
</view>
