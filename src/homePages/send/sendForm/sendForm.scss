.sendPackage {
  width: 750upx;
  display: flex;
  flex-direction: column;

  .page-item {
    flex: 1;
    width: 750upx;
    overflow-y: auto;
    margin-bottom: 118upx;
  }
  //柜机信息
  .cabinet {
    width: 690upx;
    height: 145upx;
    margin-left: 30upx;
    margin-top: 30upx;
    background: #FFFFFF;
    border-radius: 10upx;
    display: flex;
    box-sizing: border-box;
    flex-direction: column;
    padding: 18upx 20upx 0 20upx;
    .icon-btn2 {
      display: flex;
      flex-direction: column;
      width: 52upx;
      height: 100%;
      margin-top: 5upx;
      justify-content: center;
      align-items: center;
      image {
        width: 38upx;
        height: 38upx;
        margin-bottom: 7upx;
      }

      text {
        color: black;
        font-size: 28upx;
      }

      .text1 {
        color: black;
        font-size: 17upx;
      }
    }

    .icon-border {
      width: 1upx;
      height: 55upx;
      border-left: 2px solid #FFFFFF;
      margin-left: 32upx;
      margin-right: 26upx;
    }
  }
  //地址信息
  .address {
    width: 690upx;
    margin-left: 30upx;
    margin-top: 30upx;
    height: 300upx;
    background: #FFFFFF;
    border-radius: 10upx;
    display: flex;
    flex-direction: column;
    padding: 0upx 30upx;
    box-sizing: border-box;

    .address-item {
      flex: 1;
      display: flex;
      flex-direction: row;
      align-items: center;

      image {
        width: 50upx;
        height: 50upx;
      }

      .address-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-left: 20upx;

        .name {
          font-size: 28upx;
          font-weight: 400;
          color: #000000;
          max-width: 400upx;
          flex-wrap: wrap;
        }

        .info {
          margin-top: 4upx;
          font-size: 22upx;
          font-weight: 400;
          color: #999999;
          overflow: hidden;
          max-width: 490upx;
          text-overflow: ellipsis;
          word-wrap: normal;
          white-space: pre-wrap;
        }
      }

      .address-book {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 55upx;
        border-left: 4upx solid #EEEEED;
        padding-left: 20upx;

        image {
          width: 39upx;
          height: 41upx;
        }
      }
    }

    .border {
      width: 630upx;
      height: 1upx;
      background-color: #EEEEED;
    }
  }

  .company{
    width: 690upx;
    height: 118upx;
    margin-left: 30upx;
    margin-top: 30upx;
    background: #FFFFFF;
    border-radius: 10upx;
    display: flex;
    box-sizing: border-box;
    flex-direction: column;
    padding: 18upx 20upx 0 20upx;
  }

  //订单信息
  .package {
    width: 690upx;
    height: 403upx;
    margin-left: 30upx;
    margin-top: 30upx;
    background: #FFFFFF;
    border-radius: 10upx;
    display: flex;
    box-sizing: border-box;
    flex-direction: column;
    padding: 18upx 20upx 0 20upx;
  }
  .cabinet-loc {
    height: 110upx;
    background: #FFF3EF;
    border-radius: 10upx;
    display: flex;
    flex-direction: row;
    box-sizing: border-box;
    padding: 19upx 10upx;
    align-items: center;
    .loc{
      width: 56upx;
      height: 56upx;
      margin-right: 15upx;
    }
    .name {
      display: flex;
      flex-direction: row;
      line-height: 36upx;
      vertical-align: bottom;
      font-size: 28upx;
      font-weight: 400;
      color: #585858;

      image {
        width: 58upx;
        height: 30upx;
        margin-top: 2upx;
      }
    }

    .icon-btn {
      display: flex;
      flex-direction: column;
      width: 52upx;
      height: 100%;
      margin-top: 5upx;
      justify-content: center;
      align-items: center;

      image {
        width: 38upx;
        height: 38upx;
        margin-bottom: 7upx;
      }

      text {
        color: black;
        font-size: 18upx;
      }

      .text1 {
        color: black;
        font-size: 17upx;
      }
    }

    .icon-border {
      width: 1upx;
      height: 55upx;
      border-left: 2px solid #FFFFFF;
      margin-left: 32upx;
      margin-right: 26upx;
    }
  }

  .space-two {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 91upx;
    margin-left: 30upx;
    margin-right: 30upx;

    .title {
      font-size: 28upx;
      font-weight: 400;
      color: #000000;
    }
    .boxType{
      background-color: $config-color;
      padding: 2upx 20upx;
      border-radius: 10upx;
      display: flex;
      align-items: center;
      font-size: 22upx;
      font-weight: 400;
      color: #ffffff;
      margin-left: 10upx;
    }

    .right {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;

      .box {
        height: 34upx;
        border: 1upx solid $config-color;
        border-radius: 30upx;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1upx 12upx;
        margin-right: 20upx;

        text {
          font-size: 18upx;
          font-weight: 400;
          color: $config-color;
        }
      }

      .icon {
        margin-left: 20upx;
        font-size: 28upx;
        color: black;
      }
    }
  }

  .space-one-start {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    min-height: 91upx;
    margin-left: 30upx;
    margin-right: 30upx;
  }

  .remarks-bg {
    width: 610upx;
    height: 200upx;
    margin-left: 30upx;
    margin-right: 30upx;
    background: #F5F5F5;
    border-radius: 10upx;

    ::v-deep.u-textarea {
      background-color: transparent !important;

      .u-textarea__count {
        background-color: transparent !important;
      }
    }
  }

  .bottom-width {
    border-bottom: 1px solid #EEEEED;
  }

  //其他信息
  .real-name {
    width: 690upx;
    height: 388upx;
    background: #FFFFFF;
    margin-left: 30upx;
    margin-top: 30upx;
    box-sizing: border-box;
    border-radius: 10upx;
    padding: 0 20upx;
  }

  //授权
  .auth2 {
    width: 690upx;
    margin: 40upx 30upx;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    line-height: 44upx;
    text {
      font-size: 26upx;
      font-weight: 400;
      color: #000000;
    }
    .icons {
      font-size: 38upx;
      margin-right: 12upx;
      color: #999999
    }
    .al {
      color: $config-color;
    }
  }

  //底部按钮
  .bottom {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: row;
    height: 118upx;
    width: 750upx;
    background-color: #ffffff;
    padding: 10upx 30upx;
    box-sizing: border-box;

    .left {
      display: flex;
      flex-direction: column;
      flex: 1;

      .price-title {
        font-size: 30upx;
        font-weight: 400;
        color: #000000;
      }

      .price {
        color: $config-color;
      }

      .content {
        font-size: 22upx;
        font-weight: 400;
        color: #999999;
      }
    }

    .right {
      .button {
        width: 330upx;
        height: 76upx;
        background: #FFE6DE;
        border-radius: 38upx;
        display: flex;
        justify-content: center;
        align-items: center;
        border: none;

        text {
          font-size: 30upx;
          font-weight: 400;
          color: #FFFFFF;
        }
      }

      .active {
        background: $config-color;
      }
    }
  }

  .pop {
    .pop1 {
      padding: 50upx 30upx 30upx 30upx;
      display: flex;
      height: 410upx;
      box-sizing: border-box;
      flex-direction: column;

      .list {
        display: flex;
        flex-direction: row;
        justify-content: space-around;

        .item {
          width: 260upx;
          height: 200upx;
          background: #FFFFFF;
          border: 1upx solid #B5B5B5;
          box-sizing: border-box;
          border-radius: 10upx;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          image {
            width: 41upx;
            height: 41upx;
          }

          .title {
            font-size: 30upx;
            font-weight: 500;
            color: #000000;
          }

          .content {
            font-size: 20upx;
            font-weight: 400;
            color: #999999;
          }

          .sub-title {
            font-size: 24upx;
            font-weight: 400;
            color: #000000;
          }

          .mt15 {
            margin-top: 15upx;
          }

          .mt10 {
            margin-top: 10upx;
          }

          .active {
            color: $config-color;
          }
        }

        .active-bor {
          border: 1upx solid $config-color;
          color: $config-color;
        }
      }

    }

    .pop2 {
      padding: 40upx 30upx 30upx 30upx;
      display: flex;
      height: 546upx;
      box-sizing: border-box;
      flex-direction: column;
      align-items: center;

    }

    .pop3 {
      padding: 40upx 30upx 30upx 30upx;
      display: flex;
      height: 750upx;
      box-sizing: border-box;
      flex-direction: column;
      align-items: center;

      .center-item {
        display: flex;
        flex-direction: column;

        .package-list {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;

          .package-item {
            width: 200upx;
            height: 70upx;
            background: #f5f5f5;
            //border: 1px solid #B5B5B5;
            border-radius: 35upx;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 27upx;
            margin-top: 18upx;

            text {
              font-size: 28upx;
              font-weight: 400;
              color: #999999;
            }
          }

          .active-package-item {
            background-color: $config-color;

            text {
              color: #ffffff;
            }
          }
        }
      }

      .weight-button {
        display: flex;
        flex-direction: row;
        flex: 1;
        justify-content: center;
        align-items: center;

        .num-btn {
          width: 114upx;
          height: 80upx;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #EEEEED;
          font-size: 36upx;
        }

        .num-btn:first-child {
          border-bottom-left-radius: 10upx;
          border-top-left-radius: 10upx;
        }

        .num-btn:last-child {
          border-bottom-right-radius: 10upx;
          border-top-right-radius: 10upx;
        }

        .amount-view {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 80upx;
          width: 268upx;
          background-color: #f5f5f5;
          font-size: 36upx;
          font-weight: 400;
          padding-left: 90upx;
          box-sizing: border-box;
          color: black;

          .weight-flag {
            font-weight: unset;
            font-size: 32upx;
            margin-left: 50upx;
          }
        }
      }
    }

    .center {
      font-size: 34upx;
      font-weight: 400;
      color: #000000;
    }

    .middle {
      flex: 1;
      display: flex;
      flex-direction: row;

      .item {
        flex: 1;
        width: 354upx;

        .scroll-view {
          height: 300upx;

          .brand-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            height: 84upx;

            image {
              width: 54upx;
              height: 54upx;
            }

            text {
              font-size: 30upx;
              margin-left: 15px;
              font-weight: 400;
              color: #000000;
            }

            .no-margin {
              margin-left: 0;
            }
          }

          .active-select {
            width: 329upx;
            background-color: #f5f5f5;
            border-radius: 20upx;
            height: 84upx;
          }
        }
      }
    }

    .btn {
      width: 690upx;
      height: 74upx;
      margin-top: 40upx;
      line-height: 74upx;
      box-sizing: border-box;
      border-radius: 45upx;
      text-align: center;
      font-size: $uni-font-size-sm;
    }

    .primary {
      color: #ffffff;
      background-color: $config-color;
    }
  }
  .color-active {
    color:  $config-color;
  }
}
