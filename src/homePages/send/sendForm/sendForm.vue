<script lang="ts">
import 'reflect-metadata';
import {Component, Mixins, Watch} from 'vue-property-decorator';
import {ImageMixins} from "@/mixins/ImageMixins";
import {addressInfo, cabinetForm} from "@/utils/types";
import InputWidget from "@/components/input-widget/index.vue";
import Config from "@/utils/config";
import {Modal} from '@/utils';
import {saveOrder, getDefaultAddress} from "@/model/send";
import {PackageMixins} from "@/mixins/packageMixin";
import config from "@/utils/config";

// 地址类型
export enum AddressType {
  SENDER = 'sender',
  RECEIVE = 'receive'
}

@Component({
  name: 'sendForm',
  components: {InputWidget}
})
export default class SendForm extends Mixins(ImageMixins, PackageMixins) {
  // private showBottomSelect: boolean = false
  // private popType: string = ''
  private locationAuth: boolean = false
  private isAgree: boolean = false
  private isEdit: boolean = false
  private addressType: any = AddressType
  private sender: addressInfo = new addressInfo()
  private orderInfo: any = {}
  // private packageTypeMap: Map<any, any> = new Map([[1, '文件'], [2, '电子类产品'], [3, '生活用品'], [4, '服饰'], [5, '生鲜'], [6, '食品'], [7, '易碎品'], [8, '化妆品'], [9, '药品'], [10, '其他']])
  private receiver: addressInfo = new addressInfo()
  private cabinet: cabinetForm = new cabinetForm()
  // private packageInfo: any = {
  //   brand: 'JT',
  //   packageType: 1,
  //   packageWeight: 1,
  //   packageAmount: 1,
  //   payMethod: 1,
  //   cabinetBoxType: 1
  // }
  private brandInfo: any = {
    brandCode: '',
    boxType: -1,
    price: 0
  }
  private otherInfo: any = {
    isRealName: 1,
    remarks: ''
  }

  get canSave() {
    return this.sender.name !== '' && this.receiver.name !== '' && this.brandInfo.brandCode !== '' && this.isAgree
  }

  // get packageTypeList() {
  //   let list: any[] = []
  //   this.packageTypeMap.forEach((value, key) => {
  //     list.push({
  //       name: value,
  //       value: key
  //     })
  //   })
  //   return list
  // }

  // get brandList() {
  //   let list: any[] = Object.keys(Config.EXPRESS).map((key: any) => {
  //     return {
  //       brandName: Config.EXPRESS[key],
  //       brandCode: key,
  //       brandLogo: Utils.getCompanyLogo(key)
  //     }
  //   })
  //   return list
  // }

  // get cabinetBoxList() {
  //   let list: any[] = []
  //   Config.BoxLabel.forEach((value, key) => {
  //     list.push({
  //       boxType: key,
  //       boxName: value + '格口',
  //       price: 10000
  //     })
  //   })
  //   return list
  // }

  // @Watch('packageInfo.packageWeight', {deep: true, immediate: true})
  // getPackageAmountChange(newVal: number, oldVal: number) {
  //   if (newVal === 0) {
  //     this.packageInfo.packageWeight = 1
  //   }
  // }
  //
  // setCabinetBoxType(item: any) {
  //   this.packageInfo.cabinetBoxType = item.boxType
  // }

  goCabinetLocationPage() {
    uni.navigateTo({url: '/homePages/send/cabinetLocationList/cabinetLocationList?isBack=true'})
  }

  getAddressUser(type: AddressType) {
    let label = ''
    if (type === AddressType.SENDER) {
      label = '寄件人信息'
      if (this.sender.name !== '' && this.sender.mobile != '') {
        label = this.sender.name + '\u3000' + this.sender.mobile
      }
    } else if (type === AddressType.RECEIVE) {
      label = '收件人信息'
      if (this.receiver.name !== '' && this.receiver.mobile != '') {
        label = this.receiver.name + '\u3000' + this.receiver.mobile
      }
    }
    return label
  }

  // openPop(type: string) {
  //   this.showBottomSelect = true
  //   this.popType = type
  // }


  goAddressEdit(type: string) {
    let url: string = '/homePages/send/addressForm/addressForm?isBack=true&type=' + type
    if (type === 'send') {
      if (this.sender.name) {
        url += `&addressInfo=${encodeURIComponent(JSON.stringify(this.sender))}`
      }
    } else {
      if (this.receiver.name) {
        url += `&addressInfo=${encodeURIComponent(JSON.stringify(this.receiver))}`
      }
    }
    if (!this.isEdit) {
      uni.navigateTo({url: url})
    } else {
      this.goAddressList(type)
    }
  }

  goAddressList(type: string) {
    uni.navigateTo({url: '/homePages/send/addressList/addressList?type=' + type})
  }

  // getPayMethod() {
  //   let label = '请选择'
  //   if (this.packageInfo.payMethod > 0) {
  //     let map: Map<any, any> = new Map([[1, '到柜支付'], [2, '先寄后付']])
  //     label = map.get(this.packageInfo.payMethod)
  //   }
  //   return label
  // }

  getBrandInfo() {
    return this.getBrandName()
  }

  getBoxType() {
    if (this.brandInfo.boxType > 0) {
      return Config.BoxLabel.get(this.brandInfo.boxType) + '格'
    } else {
      return ''
    }
  }

  openBrandPage() {
    uni.navigateTo({url: '/homePages/send/brandSelect/brandSelect?cabinetInfo=' + encodeURIComponent(JSON.stringify(this.cabinet))})
  }

  // changePayMethod(type: number) {
  //   this.packageInfo.payMethod = type
  // }

  getAddressInfo(type: AddressType) {
    let label = ''
    if (type === AddressType.SENDER) {
      label = '点击填写寄件人信息'
      if (this.sender.address !== '') {
        label = this.getAddress(this.sender.provinceName, this.sender.cityName, this.sender.areaName, this.sender.streetName, this.sender.address)
      }
    } else if (type === AddressType.RECEIVE) {
      label = '点击填写收件人信息'
      if (this.receiver.address !== '') {
        label = this.getAddress(this.receiver.provinceName, this.receiver.cityName, this.receiver.areaName, this.receiver.streetName, this.receiver.address)
      }
    }
    return label
  }

  // getCabinetBoxLabel() {
  //   return Config.BoxLabel.get(this.packageInfo.cabinetBoxType) + '格口'
  // }

  // getPackageType() {
  //   let label = '请选择'
  //   if (this.packageInfo.packageType !== '') {
  //     label = this.packageTypeMap.get(this.packageInfo.packageType)
  //   }
  //   return label
  // }

  // geIsRealName() {
  //   let label = '已实名'
  //   if (this.otherInfo.isRealName === 0) {
  //     label = '未实名'
  //   }
  //   return label
  // }

  getBrandName() {
    let label = '请选择'
    if (this.brandInfo.brandCode !== '' && this.brandInfo.brandCode !== null) {
      return Config.EXPRESS[this.brandInfo.brandCode]
    }
    return label
  }

  // getDistance() {
  //   let str = ''
  //   if (this.cabinet.distance >= 1000) {
  //     str = (this.cabinet.distance / 1000).toFixed(1) + 'km'
  //   } else {
  //     str = this.cabinet.distance.toFixed(0) + 'm'
  //   }
  //   return str
  // }


  goDoc() {
    let url=`/pages/main/webView?url=${config.agreement}/#/sendProtocol`
    uni.navigateTo({url})
    // uni.navigateTo({url: '/authPages/sendAgreement/sendAgreement'})
  }

  agreeAuth() {
    this.isAgree = !this.isAgree
  }

  async getDefaultSender() {
    const res = await getDefaultAddress();
    if (res) {
      this.sender = res
    }
  }

  getFormInfo() {
    return {
      cabinetLocationCode: this.cabinet.code,
      remarks: this.otherInfo.remarks,
      cabinetBoxType: this.brandInfo.boxType,
      brandCode: this.brandInfo.brandCode,
      brandName: Config.EXPRESS[this.brandInfo.brandCode],
      senderName: this.sender.name,
      senderMobile: this.sender.mobile,
      senderProvinceCode: this.sender.provinceCode,
      senderProvinceName: this.sender.provinceName,
      senderCityCode: this.sender.cityCode,
      senderCityName: this.sender.cityName,
      senderAreaCode: this.sender.areaCode,
      senderAreaName: this.sender.areaName,
      senderStreetCode: this.sender.streetCode,
      senderStreetName: this.sender.streetName,
      senderAddress: this.sender.address,
      receiverName: this.receiver.name,
      receiverMobile: this.receiver.mobile,
      receiverProvinceCode: this.receiver.provinceCode,
      receiverProvinceName: this.receiver.provinceName,
      receiverCityCode: this.receiver.cityCode,
      receiverCityName: this.receiver.cityName,
      receiverAreaCode: this.receiver.areaCode,
      receiverAreaName: this.receiver.areaName,
      receiverStreetCode: this.receiver.streetCode,
      receiverStreetName: this.receiver.streetName,
      receiverAddress: this.receiver.address,
    }
  }

  async saveOrder() {
    let info = this.getFormInfo()
    if (this.isEdit) {
      info = Object.assign({}, info, {id: this.orderInfo.id})
    }
    const res = await saveOrder(info)
    if (res) {
      if (this.isEdit) {
        uni.navigateTo({url: `/homePages/send/orderDetail/orderDetail?orderId=` + this.orderInfo.id})
      } else {
        uni.navigateTo({url: '/homePages/send/orderSuccess/orderSuccess?orderId=' + res.id})
      }
    }
  }

  checkOrder() {
    if (this.canSave) {
      this.saveOrder()
    } else {
      if (this.sender.name == '') {
        Modal.toast('请填写寄件人信息')
        return false;
      }
      if (this.receiver.name == '') {
        Modal.toast('请填写收件人信息')
        return false;
      }
      if (this.brandInfo.brandCode == '') {
        Modal.toast('请选择快递公司与格口')
        return false;
      }
      if (!this.isAgree) {
        Modal.toast('请同意服务协议')
        return false;
      }
    }
  }

  initData(data: string) {
    let order: any = JSON.parse(decodeURIComponent(data))
    this.orderInfo = order
    this.sender = {
      name: order.senderName,
      mobile: order.senderMobile,
      text: '',
      address: order.senderAddress,
      provinceCode: order.senderProvinceCode,
      provinceName: order.senderProvinceName,
      cityCode: order.senderCityCode,
      cityName: order.senderCityName,
      areaCode: order.senderAreaCode,
      areaName: order.senderAreaName,
      streetCode: order.senderStreetCode,
      streetName: order.senderStreetName,
      hasDefault: 0,
      company: ''
    } as addressInfo
    this.receiver = {
      name: order.receiverName,
      mobile: order.receiverMobile,
      text: '',
      address: order.receiverAddress,
      provinceCode: order.receiverProvinceCode,
      provinceName: order.receiverProvinceName,
      cityCode: order.receiverCityCode,
      cityName: order.receiverCityName,
      areaCode: order.receiverAreaCode,
      areaName: order.receiverAreaName,
      streetCode: order.receiverStreetCode,
      streetName: order.receiverStreetName,
      hasDefault: 0,
      company: ''
    } as addressInfo
    this.isAgree = true
    this.otherInfo = {
      remarks: order.remarks
    }
    this.brandInfo = {
      brandCode: order.brandCode,
      boxType: order.cabinetBoxType,
      price: (order.inboundUserFee / 1000).toFixed(2)
    }
  }

  formatter(value: string) {
    if (value.length > 20) {
      return value.substr(0, 20)
    }
    return value
  }

  onReady() {
    // @ts-ignore
    this.$refs.textarea.setFormatter(this.formatter)
  }

  onShow() {
    const that = this
    uni.authorize({
      scope: 'scope.userLocation',
      success() {
        that.locationAuth = true
        // that.getLocation()
      },
      fail() {
        that.locationAuth = false
      }
    })
  }

  onLoad(e: any) {
    if (e.cabinetInfo) {
      this.cabinet = JSON.parse(decodeURIComponent(e.cabinetInfo))
    }
    if (e.orderInfo) {
      this.isEdit = true
      this.initData(e.orderInfo)
    } else {
      this.getDefaultSender()
    }
  }
}
</script>

<template src="./sendForm.html"></template>
<style src="./sendForm.scss" lang="scss" scoped></style>
