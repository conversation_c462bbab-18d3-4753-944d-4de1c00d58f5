<view class="storageOrder">
    <view class="storageOrder-view">
        <view class="cabinet">
            <view class="cabinet-loc">
                <img :src="getImage('location')" alt="" class="loc">
                <view class="name" style="flex: 1">
                    已选柜机：
                    <span> {{ title }}</span>
                </view>
            </view>
        </view>
        <view class="storageOrder-item">
            <view class="storageOrder-item-title">请选择格口类型</view>
            <view class="storageOrder-item-body">
                <button
                        v-for="(item,index) in latticeList"
                        :key="index"
                        :class="item.id===latticeValue? 'item-body-view checked':'item-body-view' "
                        :disabled="item.display"
                        @click="setLattice(item)"
                >
                    <view>{{ item.name }}</view>
                    <!--<view>{{ item.value }}元/小时</view>-->
                </button>
            </view>
            <view v-if="latticeValue" class="tips">
                {{
                    latticeValue === '0' ?
                            `首期${keepJson.supers.firstHour + keepJson.supers.secondHour}小时，共${((keepJson.supers.firstHour * (keepJson.supers.firstInterval / 1000)) + keepJson.supers.secondHour * (keepJson.supers.secondInterval / 1000)).toFixed(2)}元，之后每小时${(keepJson.supers.intervalFee / 1000).toFixed(2)}元，封顶费${(keepJson.supers.maxFee / 1000).toFixed(2)}元`
                            : ''
                }}
                {{
                    latticeValue === '1' ?
                            `首期${keepJson.huge.firstHour + keepJson.huge.secondHour}小时，共${((keepJson.huge.firstHour * (keepJson.huge.firstInterval / 1000)) + keepJson.huge.secondHour * (keepJson.huge.secondInterval / 1000)).toFixed(2)}元，之后每小时${(keepJson.huge.intervalFee / 1000).toFixed(2)}元，封顶费${(keepJson.huge.maxFee / 1000).toFixed(2)}元`
                            : ''
                }}
                {{
                    latticeValue === '2' ?
                            `首期${keepJson.large.firstHour + keepJson.large.secondHour}小时，共${((keepJson.large.firstHour * (keepJson.large.firstInterval / 1000)) + keepJson.large.secondHour * (keepJson.large.secondInterval / 1000)).toFixed(2)}元，之后每小时${(keepJson.large.intervalFee / 1000).toFixed(2)}元，封顶费${(keepJson.large.maxFee / 1000).toFixed(2)}元`
                            : ''
                }}
                {{
                    latticeValue === '3' ?
                            `首期${keepJson.medium.firstHour + keepJson.medium.secondHour}小时，共${((keepJson.medium.firstHour * (keepJson.medium.firstInterval / 1000)) + keepJson.medium.secondHour * (keepJson.medium.secondInterval / 1000)).toFixed(2)}元，之后每小时${(keepJson.medium.intervalFee / 1000).toFixed(2)}元，封顶费${(keepJson.medium.maxFee / 1000).toFixed(2)}元`
                            : ''
                }}
                {{
                    latticeValue === '4' ?
                            `首期${keepJson.small.firstHour + keepJson.small.secondHour}小时，共${((keepJson.small.firstHour * (keepJson.small.firstInterval / 1000)) + keepJson.small.secondHour * (keepJson.small.secondInterval / 1000)).toFixed(2)}元，之后每小时${(keepJson.small.intervalFee / 1000).toFixed(2)}元，封顶费${(keepJson.small.maxFee / 1000).toFixed(2)}元`
                            : ''
                }}
                {{
                    latticeValue === '5' ?
                            `首期${keepJson.mini.firstHour + keepJson.mini.secondHour}小时，共${((keepJson.mini.firstHour * (keepJson.mini.firstInterval / 1000)) + keepJson.mini.secondHour * (keepJson.mini.secondInterval / 1000)).toFixed(2)}元，之后每小时${(keepJson.mini.intervalFee / 1000).toFixed(2)}元，封顶费${(keepJson.mini.maxFee / 1000).toFixed(2)}元`
                            : ''
                }}
                {{
                    latticeValue === '6' ?
                            `首期${keepJson.micro.firstHour + keepJson.micro.secondHour}小时，共${((keepJson.micro.firstHour * (keepJson.micro.firstInterval / 1000)) + keepJson.micro.secondHour * (keepJson.micro.secondInterval / 1000)).toFixed(2)}元，之后每小时${(keepJson.micro.intervalFee / 1000).toFixed(2)}元，封顶费${(keepJson.micro.maxFee / 1000).toFixed(2)}元`
                            : ''
                }}
            </view>
        </view>
        <view class="storageOrder-item">
            <view class="storageOrder-item-title">储物信息</view>
            <uni-forms ref="subForm" :model-value="fromData">
                <uni-forms-item name="sendMobile">
                    <view class="from-item">
                        <view>存件人</view>
                        <input v-model="fromData.sendMobile" placeholder="请输入存件人手机号" type="number"/>
                    </view>
                </uni-forms-item>
                <uni-forms-item name="collectMobile">
                    <view class="from-item">
                        <view>取件人</view>
                        <input v-model="fromData.collectMobile" placeholder="请输入取件人手机号" type="number"/>
                    </view>
                </uni-forms-item>
                <uni-forms-item name="timeValue">
                    <view class="from-item">
                        <view>储物时间</view>
                        <input v-model="fromData.timeValue" maxlength="3" placeholder="请输入储物时间" type="number"
                               @input="setTime"/>
                    </view>
                </uni-forms-item>
            </uni-forms>
            <view>
                <view class="storageOrder-item-input">
                    <view class="view">
                        <u-radio-group
                                v-model="radioValue"
                                placement="row"
                                @change="groupChange"
                        >
                            <u-radio
                                    v-for="(item, index) in radioList"
                                    :key="index"
                                    :activeColor="configColor"
                                    :customStyle="{marginRight: '8px',marginTop:'8px'}"
                                    :label="item.name"
                                    :name="item.name"
                            >
                            </u-radio>
                        </u-radio-group>
                    </view>
                </view>
                <view class="storageOrder-item-input ">
                    到期时间：{{ expireTime }}
                </view>
            </view>
        </view>
    </view>
    <view class="storageOrder-footer">
        <view class="footer-btn" @click="creatKeepOrder">确定</view>
    </view>
</view>