.storageOrder{
  width: 100%;

  .storageOrder-view{
    padding: 20upx 20upx 120upx;

    .cabinet {
      background: #FFFFFF;
      border-radius: 10upx;
      display: flex;
      box-sizing: border-box;
      flex-direction: column;
      padding:20upx;
      margin-bottom: 20upx;

      .icon-btn2 {
        display: flex;
        flex-direction: column;
        width: 52upx;
        height: 100%;
        margin-top: 5upx;
        justify-content: center;
        align-items: center;
        image {
          width: 38upx;
          height: 38upx;
          margin-bottom: 7upx;
        }

        text {
          color: black;
          font-size: 28upx;
        }

        .text1 {
          color: black;
          font-size: 17upx;
        }
      }

      .icon-border {
        width: 1upx;
        height: 55upx;
        border-left: 2px solid #FFFFFF;
        margin-left: 32upx;
        margin-right: 26upx;
      }
    }

    .cabinet-loc {
      height: 110upx;
      background: #FFF3EF;
      border-radius: 10upx;
      display: flex;
      flex-direction: row;
      box-sizing: border-box;
      padding: 19upx 10upx;
      align-items: center;
      .loc{
        width: 56upx;
        height: 56upx;
        margin-right: 15upx;
      }
      .name {
        display: flex;
        flex-direction: row;
        line-height: 36upx;
        vertical-align: bottom;
        font-size: 28upx;
        font-weight: 400;
        color: #585858;

        image {
          width: 58upx;
          height: 30upx;
          margin-top: 2upx;
        }
      }

      .icon-btn {
        display: flex;
        flex-direction: column;
        width: 52upx;
        height: 100%;
        margin-top: 5upx;
        justify-content: center;
        align-items: center;

        image {
          width: 38upx;
          height: 38upx;
          margin-bottom: 7upx;
        }

        text {
          color: black;
          font-size: 18upx;
        }

        .text1 {
          color: black;
          font-size: 17upx;
        }
      }

      .icon-border {
        width: 1upx;
        height: 55upx;
        border-left: 2px solid #FFFFFF;
        margin-left: 32upx;
        margin-right: 26upx;
      }
    }


    .storageOrder-item{
      padding: 20upx;
      background-color: #FFFFFF;
      margin-bottom: 20upx;
      border-radius: 5upx;

      .storageOrder-item-title{
        font-weight: 500;
        font-size: 30upx;
      }

      .from-item{
        padding: 5px 0;
        display: flex;
        align-items: center;
        border-bottom: 2upx solid #F5F5F5;
        height: 35px;

        view{
          width: 25%;
        }
      }

      .storageOrder-item-body{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20upx;

        .item-body-view{
          width: 20%;
          margin: 0;
          padding: 10upx;
          text-align: center;
          font-size: 28upx;
          height: 96upx;
          line-height: 80upx;
          border-radius: 0;
        }

        button::after{
          border-radius: 0;
        }

        .checked{
          background-color: $config-color;
          color: #FFFFFF;
        }
      }

      .tips{
        margin-top: 20upx;
        color: #ee3333;
      }

      .storageOrder-item-input{
        display: flex;
        justify-content: space-between;
        margin-top: 20upx;

        .view{
          width: 75%;
          display: flex;
          align-items: center;

          .tips{
            margin-top: 20upx;
            color: #ee3333;
          }

          input{
            border: 2upx solid #e3e3e3;
            padding: 10upx 20upx;
          }
        }
      }
    }
  }

  .storageOrder-footer{
    width: 95%;
    position: fixed;
    bottom: 0;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20upx;

    .footer-btn{
      background-color: $config-color;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 80upx;
      width: 100%;
      border-radius: 50upx;
    }
  }
}