<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import SearchInput from "@/components/search-input/index.vue"
import {ImageMixins} from "@/mixins/ImageMixins";
import {creatCabinetKeepOrder, getCabinetInfo} from "@/model/cabinet";
import modal from "@/utils/core/modal";
import {usableSummary} from "@/model/send";
import UniForms from "@/components/uni-forms/uni-forms.vue";
import UniFormsItem from "@/components/uni-forms-item/uni-forms-item.vue";
import Cache from "../../../utils/core/cache";
import InputWidget from "@/components/input-widget/index.vue";
import config from "@/config";


@Component({
  name: 'nearCabinet',
  components: {
    SearchInput, UniForms, UniFormsItem, InputWidget,
  }
})
export default class NearCabinet extends ImageMixins {
  $refs!: {
    subForm: HTMLFormElement
  }
  private configColor = config.color
  private latticeList = [
    {id: '6', name: '极小格', value: 0.03, display: false},
    {id: '5', name: '超小格', value: 0.03, display: false},
    {id: '4', name: '小格', value: 0.03, display: false},
    {id: '3', name: '中格', value: 0.03, display: false},
    {id: '2', name: '大格', value: 0.03, display: false},
    {id: '1', name: '超大格', value: 0.03, display: false},
    {id: '0', name: '极大格', value: 0.03, display: false},
  ]
  private latticeValue: any = ''
  private fromData: any = {
    sendMobile: '',
    collectMobile: '',
    timeValue: '',
  }
  private radioList = [
    {
      name: '小时',
      disabled: false
    },
    {
      name: '天',
      disabled: false
    },
  ]
  private radioValue = '小时'
  private expireTime: any = ''
  private cabinetLocationCode = ''
  private keepJson = {}
  private title = ''
  private rules: any = {
    sendMobile: {
      rules: [{required: true, errorMessage: '请输入发件人手机号'}, {
        validateFunction: function (rule: any, value: string, data: any, callback: (arg0: string) => void) {
          let regIdNo = /^1[3456789]\d{9}$/
          if (!regIdNo.test(value)) {
            callback('手机号格式错误')
          }
          return true
        }
      }]
    },
    collectMobile: {
      rules: [{required: true, errorMessage: '请输入取件人手机号'}, {
        validateFunction: function (rule: any, value: string, data: any, callback: (arg0: string) => void) {
          let regIdNo = /^1[3456789]\d{9}$/
          if (!regIdNo.test(value)) {
            callback('手机号格式错误')
          }
          return true
        }
      }]
    },
    timeValue: {
      rules: [{required: true, errorMessage: '请输入储物时间'}, {
        validateFunction: function (rule: any, value: string, data: any, callback: (arg0: string) => void) {
          let regIdNo = /^[0-9]*$/
          if (!regIdNo.test(value)) {
            callback('请输入储物时间')
          }
          return true
        }
      }]
    }
  }


  //创建储物订单
  async creatKeepOrder() {
    if (this.latticeValue === '') {
      modal.toast('请选择格口类型')
    } else {
      this.$refs.subForm.validate(async (valid: any) => {
        if (!valid && this.fromData.timeValue && this.fromData.sendMobile && this.fromData.collectMobile) {
          let keepHour
          if (this.radioValue === '小时') {
            keepHour = this.fromData.timeValue
          } else {
            keepHour = this.fromData.timeValue * 24
          }
          let data: any = {
            senderPhone: this.fromData.sendMobile,
            keepHour: keepHour,
            receiverMobile: this.fromData.collectMobile,
            boxType: this.latticeValue / 1,
            cabinetLocationCode: this.cabinetLocationCode
          }
          let res = await creatCabinetKeepOrder(data)
          let info = {
            orderId: res.orderId,
            tradeNo: res.tradeNo,
            totalFee: res.totalFee,
            cabinetLocationCode: this.cabinetLocationCode
          }
          if (res) {
            if (res.totalFee === '0') {
              uni.navigateTo({url: '/homePages/storageOrder/storageOrderStatus/storageOrderStatus?info=' + JSON.stringify(info)})
            } else {
              wx.requestPayment({
                timeStamp: res.timeStamp,
                nonceStr: res.nonceStr,
                package: res.packageValue,
                signType: res.signType,
                paySign: res.paySign,
                success: function (res) {
                  uni.navigateTo({url: '/homePages/storageOrder/storageOrderStatus/storageOrderStatus?info=' + JSON.stringify(info)})
                }
              })
            }
          }
        }
      })
    }
  }

  //设置到期时间单位
  groupChange(n: any) {
    const date = new Date().getTime() / 1000
    if (this.fromData.timeValue !== '') {
      if (n === '天') {
        let time = date + this.fromData.timeValue * 86400
        this.expireTime = this.format(time)
      } else {
        let time = date + this.fromData.timeValue * 3600
        this.expireTime = this.format(time)
      }
    } else {
      this.expireTime = ''
    }
  }

  // 设置到期时间
  setTime(e: any) {
    this.groupChange(this.radioValue)
  }

  // 时间戳转 年/月/日
  format(inputTime: any) {
    if (inputTime !== 0) {
      const add0 = (m: string | number) => {
        return Number(m) < 10 ? '0' + m : Number(m) < 10 ? '0' + m : m
      }
      // 时间戳是整数，否则要parseInt转换
      let date = new Date(inputTime * 1000);
      let y = date.getFullYear();
      let m = date.getMonth() + 1;
      let d = date.getDate();
      let h = date.getHours();
      let minute = date.getMinutes();
      let second = date.getSeconds();
      return y + '.' + add0(m) + '.' + add0(d) + ' ' + add0(h) + ':' + add0(minute)
      // + ':' + add0(second);
    } else {
      return 0
    }
  }

  // 设置格口类型
  setLattice(item: any) {
    this.latticeValue = item.id
  }

  onShow() {
    const info = Cache.getItem('user')
    this.fromData.sendMobile = info.phone
  }

  onReady() {

  }

  async onLoad(option: any) {
    this.$refs.subForm.setRules(this.rules)
    const info = JSON.parse(decodeURIComponent(option.info))
    this.cabinetLocationCode = info.cabinetLocationCode
    let res = await getCabinetInfo({code: info.cabinetLocationCode})
    this.keepJson = JSON.parse(res.keepJson)
    if (!JSON.parse(res.keepJson).supers && !JSON.parse(res.keepJson).micro) {
      this.keepJson = {
        'supers': {
          'intervalFee': 0,
          'maxFee': 0,
          'firstHour': 0,
          'firstInterval': 0,
          'secondHour': 0,
          'secondInterval': 0
        },
        ...this.keepJson,
        'micro': {
          'intervalFee': 0,
          'maxFee': 0,
          'firstHour': 0,
          'firstInterval': 0,
          'secondHour': 0,
          'secondInterval': 0
        }
      }
    }
    this.title = res.name
    let useRes = await usableSummary({cabinetLocationCode: info.cabinetLocationCode})
    if (useRes.superCount === 0) {
      this.latticeList[6].display = true
    }
    if (useRes.hugeCount === 0) {
      this.latticeList[5].display = true
    }
    if (useRes.largeCount === 0) {
      this.latticeList[4].display = true
    }
    if (useRes.mediumCount === 0) {
      this.latticeList[3].display = true
    }
    if (useRes.smallCount === 0) {
      this.latticeList[2].display = true
    }
    if (useRes.miniCount === 0) {
      this.latticeList[1].display = true
    }
    if (useRes.microCount === 0) {
      this.latticeList[0].display = true
    }
  }
}
</script>

<template src="./storageOrderCreat.html"></template>
<style lang="scss" scoped src="./storageOrderCreat.scss"></style>
