<view class="storageOrderStatus">
    <view class="storageOrderStatus-title">
        <view v-if="isPayment===false" class="storageOrderStatus-tips">
            请于倒计时结束前支付：
            <u-count-down :time="time" class="time" format="ss" @change="onchange" @finish="finish">
                <text class="time__item">{{ timeData.seconds }}&nbsp;S</text>
            </u-count-down>
        </view>
        <view class="storageOrderStatus-header">
            <u-icon color="#FF5E24" name="checkmark-circle-fill" size="60"></u-icon>
            <view v-if="isPayment" class="storageOrderStatus-header-status">订单已支付，柜门已打开</view>
            <view v-else class="storageOrderStatus-header-status">等待订单支付状态返回，请勿退出</view>
            <view class="storageOrderStatus-header-money">￥{{ (info.totalFee / 100).toFixed(2) }}</view>
        </view>
        <view class="storageOrderStatus-body">
            <!--<view class="storageOrderStatus-body-item">-->
            <!--    <view class="label">订单号</view>-->
            <!--    <view>{{ info.orderNo }}</view>-->
            <!--</view>-->
            <view class="storageOrderStatus-body-item">
                <view class="label">流水号</view>
                <view>{{ info.tradeNo }}</view>
            </view>
            <view class="storageOrderStatus-body-item">
                <view class="label">支付方式</view>
                  <!-- #ifdef MP-WEIXIN -->
                <view>微信支付</view>
                  <!-- #endif -->
                  <!-- #ifndef MP-WEIXIN -->
                <view>支付宝支付</view>
                  <!-- #endif -->
            </view>
            <view class="storageOrderStatus-body-item">
                <view class="label">支付时间</view>
                <view>{{ info.payTime }}</view>
            </view>
            <!--        <view class="storageOrderStatus-body-item">-->
            <!--            <view class="label">订单类型</view>-->
            <!--            <view>储物订单</view>-->
            <!--        </view>-->
        </view>
        <view class="storageOrderStatus-footer">
            <view v-if="isPayment" class="storageOrderStatus-footer-btn" @click="open">重新开柜</view>
            <view v-if="isPayment" class="storageOrderStatus-footer-btn" @click="finish">关闭</view>
        </view>
    </view>
     <!-- #ifdef MP-WEIXIN -->
    <view class="advertisement">
        <ad v-if="BrandName === '熊猫智能柜'" ad-theme="white" ad-type="video" unit-id="adunit-82a9af605bd276e2"></ad>
        <ad v-if="BrandName === '便利智能柜'" ad-theme="white" ad-type="video" unit-id="adunit-546bac26972184b5"></ad>
    </view>
    <!-- #endif -->
</view>
