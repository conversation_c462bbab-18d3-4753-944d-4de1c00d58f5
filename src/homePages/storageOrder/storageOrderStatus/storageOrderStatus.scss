.storageOrderStatus {
  width: 100%;

  .storageOrderStatus-title{
    padding: 20upx;
  }

  .storageOrderStatus-tips{
    display: flex;
    align-items: center;
    padding: 20upx;
    background-color: #FFFFFF;
    margin-bottom: 20upx;

    .time__item{
      color: #ee3333;
    }
  }

  .storageOrderStatus-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #FFFFFF;
    padding: 20upx;

    .storageOrderStatus-header-status{
      font-size: 32upx;
      font-weight: 500;
      margin: 10upx;
    }

    .storageOrderStatus-header-money{
      margin-top: 10upx;
      font-size: 32upx;
      font-weight: 800;
      color: #ee3333;
    }
  }

  .storageOrderStatus-body{
    background-color: #FFFFFF;
    padding: 20upx;

    .storageOrderStatus-body-item{
      padding: 20upx 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 2upx solid #e3e3e3;

      .label{
        color: #999999;
      }
    }
  }

  .storageOrderStatus-footer{
    padding: 20upx;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .storageOrderStatus-footer-btn{
      width: 210upx;
      height: 80upx;
      border-radius: 10upx;
      color: #ffffff;
      background-color: $config-color;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}