<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import SearchInput from "@/components/search-input/index.vue"
import {ImageMixins} from "@/mixins/ImageMixins";
import {cabinetOrderOpen, cabinetOrderPaid} from "@/model/cabinet";
import modal from "@/utils/core/modal";
import Modal from "@/utils/core/modal";
import config from "@/utils/config";
import {checkPayOrder, openBoxByOrder, openSendDoor} from "@/model/send";
import {Cache} from "@/utils";

@Component({
  name: 'nearCabinet',
  components: {
    SearchInput
  }
})
export default class NearCabinet extends ImageMixins {
  private isPayment = false
  private time = 60000
  private timeData: any = {}
  private info: any = {}
  private time1 = 0
  private BrandName = config.BrandName

  onShow() {
  }


  onUnload() {
    uni.switchTab({url: '/pages/search/index/index'})
    clearInterval(this.time1)
  }


  onchange(e: any) {
    this.timeData = e
  }

  // 关闭
  finish() {
    uni.navigateBack({delta: 2})
  }

  // 打开柜门
  async open() {
    if (this.info.type === '待取列表') {
      await this.outboundPackage()
    } else if (this.info.type === '寄件订单') {
      let params: any = {
        cabinetLocationCode: this.info.cabinetLocationCode,
        orderId: this.info.orderId,
        hostIndex: Cache.getItem('hostIndex')
      }
      await this.openSendDoor(params)
    } else {
      let data = {
        cabinetLocationCode: this.info.cabinetLocationCode,
        tradeNo: this.info.tradeNo,
        hostIndex: Cache.getItem('hostIndex')
      }
      let res = await cabinetOrderOpen(data)
      if (res) {
        modal.toast('开柜成功')
      }
    }
  }

  async openSendDoor(params: any) {
    const res = await openSendDoor(params)
    if (res) {
      await Modal.alert('开柜成功')
    }
  }

  async outboundPackage() {
    let params = {
      cabinetLocationCode: this.info.cabinetLocationCode,
      checkCode: this.info.checkCode,
      hostIndex: Cache.getItem('hostIndex')
    }
    const res = await openBoxByOrder(params)
    if (res) {
      modal.toast('开柜成功')
    }
  }


  //判断订单是否支付
  async isPay(cabinetLocationCode: any, orderId: any) {
    let data = {cabinetLocationCode, orderId}
    if (this.info.type === '寄件订单') {
      const result = await checkPayOrder(data)
      if (result) {
        clearInterval(this.time1)
        this.isPayment = true
        this.info.payTime = new Date().toLocaleString() // 获取当前时间并赋值给info.payTime
      }
    } else {
      let res = await cabinetOrderPaid(data)
      if (res) {
        clearInterval(this.time1)
        this.isPayment = true
        this.info.payTime = new Date().toLocaleString() // 获取当前时间并赋值给info.payTime
        if (this.info.type !== '待取列表') {
          await this.open()
        }
      }
    }
  }

  async onLoad(option: any) {
    const info: any = JSON.parse(decodeURIComponent(option.info))
    this.info = info
    console.log(info, 8888888)
    this.time1 = setInterval(() => {
      this.isPay(info.cabinetLocationCode, info.orderId)
    }, 2000)
    await this.isPay(info.cabinetLocationCode, info.orderId)
  }
}
</script>

<template src="./storageOrderStatus.html"></template>
<style lang="scss" scoped src="./storageOrderStatus.scss"></style>
