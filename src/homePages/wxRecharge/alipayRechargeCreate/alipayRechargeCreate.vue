<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import SearchInput from "@/components/search-input/index.vue"
import {ImageMixins} from "@/mixins/ImageMixins";
import UniForms from "@/components/uni-forms/uni-forms.vue";
import UniFormsItem from "@/components/uni-forms-item/uni-forms-item.vue";
import InputWidget from "@/components/input-widget/index.vue";
import paymentService from "@/utils/services/paymentService";
import modal from "@/utils/core/modal";

@Component({
  name: 'alipayRechargeCreate',
  components: {
    SearchInput, UniForms, UniFormsItem, InputWidget,
  }
})
export default class AlipayRecharge extends ImageMixins {
  $refs!: {
    subForm: HTMLFormElement
  }
  private money = ''
  private id = ''
  private payStatus = ''
  private isShow = false
  private showBack = true

  closePopup() {
    this.isShow = false
  }

  backApp(){
    modal.toast('请手动返回APP')
  }

  async getPayInfo() {
    let that = this
    const params = {
      userId: that.id,
      money: Number(that.money) * 1000,
    };
    
    const result = await paymentService.pay(params, 'recharge');
    if (result.success) {
      that.isShow = true
      that.payStatus = 'SUCCESS'
      setTimeout(() => {
        uni.navigateBack({
          delta: 2
        })
      }, 1500)
    } else {
      that.isShow = true
      that.payStatus = 'ERROR'
    }
  }

  async onLoad(option: any) {
    if (option.id) {
      this.id = option.id
      this.money = (Number(option.money) / 1000).toFixed(2)
      this.showBack = option.showBack !== '1'
    }
  }
}
</script>

<template src="./alipayRechargeCreate.html"></template>
<style lang="scss" scoped src="./alipayRechargeCreate.scss"></style>