<view class="wxRechargeCreate">
    <view class="wxRechargeCreate-title">充值-快递员</view>
    <view class="wxRechargeCreate-price">￥{{ money }}</view>
    <view class="wxRechargeCreate-btn" @click="getPayInfo">立即支付</view>
    <button v-if="showBack" app-parameter="FAIL" binderror="launchAppError" class="wxRechargeCreate-back"
            open-type="launchApp">
        返回APP
    </button>
    <view v-else class="wxRechargeCreate-btn back" @click="backApp">返回APP</view>
    <u-popup :closeable="false" :show="isShow" mode="center" round="5">
        <view class="popup-content">
            <view class="title">支付结果</view>
            <view class="body">
                <view class="message">
                    <text class="label">{{ payStatus === 'SUCCESS' ? '支付成功!' : '支付失败!' }}</text>
                </view>
                <view v-if="payStatus ==='SUCCESS'" class="btn-view">
                    <button v-if="showBack" app-parameter="SUCCESS" binderror="launchAppError" class="btn"
                            open-type="launchApp">
                        返回APP
                    </button>
                    <view v-else class="wxRechargeCreate-btn back" @click="backApp">返回APP</view>
                </view>
                <view v-else class="btn-view">
                    <button class="btn" @click="closePopup">确定</button>
                </view>
            </view>
        </view>
    </u-popup>
</view>
