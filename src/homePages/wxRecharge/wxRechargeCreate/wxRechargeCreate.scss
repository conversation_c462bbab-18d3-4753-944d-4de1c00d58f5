.wxRechargeCreate {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 50upx;

  .wxRechargeCreate-title {
    font-size: 36upx;
    font-weight: 500;
  }

  .wxRechargeCreate-price {
    font-size: 60upx;
    font-weight: 600;
  }

  .wxRechargeCreate-btn {
    width: 80%;
    margin-top: 40upx;
    font-size: 32upx;
    background-color: $config-color;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80upx;
    border-radius: 10upx;
  }

  .back {
    background-color: #f8f8f8 !important;
    color: #1b1b1b;
    border: 2upx solid #eae5e5;
  }

  .wxRechargeCreate-back {
    width: 80%;
    margin-top: 40upx;
    font-size: 32upx;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80upx;
    border-radius: 10upx;
  }

  .popup-content {
    width: 600upx !important;
    padding-top: 30upx;

    .title {
      font-size: 36upx;
      font-weight: 600;
      text-align: center;
    }

    .body {
      font-size: 32upx;

      .message {
        padding: 30upx;
      }
    }

    .btn-view {
      border-top: 2upx solid #eee;

      .btn {
        width: 100%;
        text-align: center;
        font-size: 32upx;
        background-color: #ffffff;
      }
    }
  }
}
