<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import SearchInput from "@/components/search-input/index.vue"
import {ImageMixins} from "@/mixins/ImageMixins";
import UniForms from "@/components/uni-forms/uni-forms.vue";
import UniFormsItem from "@/components/uni-forms-item/uni-forms-item.vue";
import InputWidget from "@/components/input-widget/index.vue";
import paymentService from "@/utils/services/paymentService";
import {wxRecharge} from "@/model/cabinet";
import modal from "@/utils/core/modal";


@Component({
  name: 'wxRechargeCreate',
  components: {
    SearchInput, UniForms, UniFormsItem, InputWidget,
  }
})
// 这里class的名字是wxRechargeCreate但是写成了NearCabinet应该是个错误
// 我这边咱不做修改了 --昌昊
export default class NearCabinet extends ImageMixins {
  $refs!: {
    subForm: HTMLFormElement
  }
  private money = ''
  private id = ''
  private payStatus = ''
  private isShow = false
  private showBack = true


  onShow() {
    wx.hideHomeButton();
  }

  closePopup() {
    this.isShow = false
  }

  backApp(){
    modal.toast('请手动返回APP')
  }
  // 下面的代码是之前的代码，目前观望要不要放弃
  async getPayInfo() {
    let that = this
    wx.login({
      success: function (res) {
        const code = res.code
        let info = {
          userId: that.id,
          code: code,
          money: Number(that.money) * 1000,
        }
        wxRecharge(info).then((result) => {
          wx.requestPayment({
            timeStamp: result.timestamp,
            nonceStr: result.noncestr,
            package: result.packageValue,
            signType: result.signType,
            paySign: result.sign,
            success: async function (res) {
              that.isShow = true
              that.payStatus = 'SUCCESS'
            },
            fail: function (err) {
              console.log(err)
              that.isShow = true
              that.payStatus = 'ERROR'
            }
          })
        })
      }
    })
  }

  // 下面的代码是先用的代码，未测试代测试后放开
  // async getPayInfo() {
  //   let that = this
  //   const params = {
  //     userId: that.id,
  //     money: Number(that.money) * 1000,
  //   };
    
  //   const result = await paymentService.pay(params, 'recharge');
  //   if (result.success) {
  //     that.isShow = true
  //     that.payStatus = 'SUCCESS'
  //   } else {
  //     that.isShow = true
  //     that.payStatus = 'ERROR'
  //   }
  // }

  async onLoad(option: any) {
    if (option.id) {
      this.id = option.id
      this.money = (Number(option.money) / 1000).toFixed(2)
      this.showBack = option.showBack !== '1'
    }
  }
}
</script>

<template src="./wxRechargeCreate.html"></template>
<style lang="scss" scoped src="./wxRechargeCreate.scss"></style>
