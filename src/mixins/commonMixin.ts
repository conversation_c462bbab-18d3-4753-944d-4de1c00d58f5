import {Component, Mixins} from 'vue-property-decorator'
import Modal from "../utils/core/modal";

@Component
export class CommonMixins extends Mixins() {

    throttle(func: any, params: any = null, delay: number = 500, hasPerm: boolean = true) {
        return function () {
            // @ts-ignore
            if (getApp().globalData.clickTimer) {
                Modal.toast('手速太快啦~')
                return
            }
            if (hasPerm) {
                if (params || params === 0) {
                    func(params)
                } else {
                    func()
                }
            } else {
                Modal.toast('暂无权限')
                return
            }
            // @ts-ignore
            getApp().globalData.clickTimer = setTimeout(() => {
                // @ts-ignore
                clearTimeout(getApp().globalData.clickTimer)
                // @ts-ignore
                getApp().globalData.clickTimer = null
            }, delay)
        }()
    }

    hasPerm(perm: string): boolean {
        return true
        // if (this.user && this.user.permissions) {
        //     const {permissions} = this.user
        //     let permissionList: string[] = permissions
        //     if (permissionList) {
        //         return permissionList.indexOf(perm) > -1
        //     } else {
        //         return false
        //     }
        // } else {
        //     this.user = Cache.getItem(Config.USER_INFO, false) || {}
        //     return this.hasPerm(perm)
        // }
    }

}

