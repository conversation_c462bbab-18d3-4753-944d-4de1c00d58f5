import { Component, Mixins } from "vue-property-decorator";
import Config from "@/utils/config";
import Modal from "@/utils/core/modal";
import { createPayOrder, openSendDoor } from "@/model/send";
import { Cache } from "@/utils";
import paymentService from "@/utils/services/paymentService";

@Component
export class PackageMixins extends Mixins() {
  private cabinetBoxIsOpen: boolean = false;
  public paidOrderId: string = "";
  public cabinetLocationCode: any = "";

  get waybillNo() {
    return function (val: string) {
      if (val === "" || val == null) {
        return "暂无运单号";
      }
      return val;
    };
  }

  get orderStatus() {
    return function (val: number) {
      return Config.ORDER_STATUS[val];
    };
  }

  get orderRefundType() {
    return function (val: number) {
      return Config.ORDER_REFUND_TYPE[val];
    };
  }

  get orderRefundStatus() {
    return function (val: number) {
      return Config.ORDER_REFUND_STATUS[val];
    };
  }

  get orderRefundFee() {
    return function (val: number) {
      if (val) {
        return "￥" + (val / 1000).toFixed(2);
      }
    };
  }

  getAddressUser(name: string, phone: string) {
    let label = "";
    if (name !== "" && name != "") {
      label = name + "\u3000" + phone;
    }

    return label;
  }

  getAddress(
    province: string,
    city: string,
    area: string,
    street: string,
    address: string
  ) {
    return province + " " + city + " " + area + " " + street + "\r\n" + address;
  }

  toPackageDetail(item: any) {
    uni.navigateTo({
      url: `/homePages/send/orderDetail/orderDetail?orderId=` + item.id,
    });
  }

  toTrackList(waybillNo: string) {
    if (waybillNo) {
      uni.navigateTo({
        url: `plugin://kdPlugin/index?num=${waybillNo}&appName=熊猫AI智能柜`,
      });
    }
  }

  getTrans(path: string) {
    let cid = "";
    let code = "";
    let type = "";
    let params: any[] = decodeURIComponent(
      path.replace("pages/home/<USER>/index?scene=", "")
    ).split(",");
    params.forEach((item) => {
      if (item.startsWith("c")) {
        cid = item.split("c-")[1];
      }
      if (item.startsWith("t")) {
        type = item.split("t-")[1];
      }
      if (item.startsWith("l")) {
        code = item.split("l-")[1];
      }
      if (item.startsWith("i")) {
      }
    });
    if (type === "2") {
      return code;
    }
    return null;
  }

  resetBox() {
    this.cabinetBoxIsOpen = false;
  }

  async openSendDoor(params: any, orderId: string) {
    try {
      const res = await openSendDoor(params);
      if (res) {
        await Modal.alert("开门成功，请放入包裹");
        this.cabinetBoxIsOpen = true;
        this.paidOrderId = orderId;
      }
    } catch (error) {
      await Modal.alert("开门失败，请重试");
      console.error("开门失败:", error);
    }
  }

  async payOrder(params: any, orderId: string) {
    const that = this;
    try {
      const res = await createPayOrder(params);
      if (res) {
        wx.requestPayment({
          timeStamp: res.timeStamp,
          nonceStr: res.nonceStr,
          package: res.packageValue,
          signType: res.signType,
          paySign: res.paySign,
          success: async function (data) {
            let info = {
              orderId: orderId,
              tradeNo: res.tradeNo,
              totalFee: res.totalFee,
              cabinetLocationCode: that.cabinetLocationCode,
              type: "寄件订单",
            };
            uni.navigateTo({
              url:
                "/homePages/storageOrder/storageOrderStatus/storageOrderStatus?info=" +
                JSON.stringify(info),
            });
            // await that.openSendDoor(params, orderId)
          },
          fail: function (err) {
            Modal.toast("支付失败，请重试");
            console.error("支付失败:", err);
          },
        });
      } else {
        let info = {
          orderId: orderId,
          tradeNo: "",
          totalFee: 0,
          cabinetLocationCode: that.cabinetLocationCode,
          type: "寄件订单",
        };
        uni.navigateTo({
          url:
            "/homePages/storageOrder/storageOrderStatus/storageOrderStatus?info=" +
            JSON.stringify(info),
        });
        // await that.openSendDoor(params, orderId)
      }
    } catch (error) {
      Modal.toast("创建支付订单失败，请重试");
      console.error("创建支付订单失败:", error);
    }
  }

  async createPayOrder(orderId: string, cabinetLocationCode: string) {
    let params: any = {
      cabinetLocationCode,
      orderId,
      hostIndex: Cache.getItem("hostIndex"),
    };
    // const result = await checkPayOrder(params)
    // if (!result) {
    await this.payOrder(params, orderId);
    // } else {
    //     await this.openSendDoor(params, orderId)
    // }.

    // 以下是最新的支付方式
    // const result = await paymentService.pay(params, 'order');
    // paymentService.handleOrderPaymentResult(result, params,'寄件订单');

  }

  scanQr(orderId: any) {
    if (!orderId) {
      Modal.toast("订单ID不能为空");
      return;
    }

    uni.scanCode({
      success: async (res) => {
        if (res.path) {
          try {
            let code: string | null = this.getTrans(res.path);
            this.cabinetLocationCode = code;
            if (code) {
              await this.createPayOrder(orderId, code);
            } else {
              await Modal.alert("请扫描正确的柜机二维码");
            }
          } catch (error) {
            Modal.toast("扫码处理失败，请重试");
            console.error("扫码处理失败:", error);
          }
        } else {
          Modal.toast("无效的二维码");
        }
      },
      fail: (err) => {
        Modal.toast("扫码失败，请重试");
        console.error("扫码失败:", err);
      },
    });
  }
}
