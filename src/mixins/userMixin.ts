import {Component, Mixins} from 'vue-property-decorator'
import {userDto} from "@/utils/types";
import {getUserInfo} from '@/model/login';
import {Cache} from "@/utils";

@Component
export class UserMixins extends Mixins() {
    private user: userDto | null = null
    public isShowAuthMobile: boolean = false // 是否授权手机号
    public isRealName: boolean = false // 是否实名
    get isAuthMobile() {
        return this.user && this.user.phone != '' && this.user.phone != null
    }

    async getUserInfo(isForce = false) {
        let user: any;
        if (this.user) {
            user = this.user
        } else {
            user = await Cache.getItem('user');
        }
        let needSave: boolean = false
        if (!user) {
            user = await getUserInfo();
            needSave = true;
        }
        if (isForce) {
            user = await getUserInfo();
            needSave = true;
        }
        this.user = user
        if (this.user) {
            this.isRealName = user.hasReal === 1
        }
        if (!this.isAuthMobile) {
            this.isShowAuthMobile = true
        }
        needSave && Cache.setItem('user', user)
    }
}

