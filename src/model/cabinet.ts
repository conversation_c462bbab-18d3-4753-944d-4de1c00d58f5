import {cabinetCodeDto, keepOrder, locationDto, outboundDto, billListDto} from "@/utils/types";
import {Fetch} from "@/utils/core/fetch";
import Api from "@/utils/api";

export function creatCabinetKeepOrder(data: keepOrder): Promise<any> {
    return Fetch({url: Api.cabinetKeepOrder, data: data, method: 'POST', showLoading: true})
}

export function getCabinetList(data: locationDto): Promise<any> {
    return Fetch({url: Api.cabinetList, data: data, method: 'POST', showLoading: true})
}

export function getCabinetInfo(data: any): Promise<any> {
    return Fetch({url: Api.cabinetInfo, data: data, method: 'POST', showLoading: true})
}

export function getOrderList(data: cabinetCodeDto): Promise<any> {
    return Fetch({url: Api.orderList, data: data, method: 'POST', showLoading: true})
}

export function getSendOrderList(data: cabinetCodeDto): Promise<any> {
    return Fetch({url: Api.sendOrderList, data: data, method: 'POST', showLoading: true})
}

export function outboundOrder(data: outboundDto): Promise<any> {
    return Fetch({url: Api.outbound, data: data, method: 'POST', showLoading: true})
}

export function setCanInbound(data: any): Promise<any> {
    return Fetch({url: Api.canInbound, data: data, method: 'POST', showLoading: true})
}

export function submitCabinetLocationApply(data: any): Promise<any> {
    return Fetch({url: Api.locationApply, data: data, method: 'POST', showLoading: true})
}

export function getCabinetLocationCodeByScan(data: any): Promise<any> {
    return Fetch({url: Api.scanCode, data: data, method: 'POST', showLoading: true})
}

export function getScanCodeHostCode(data: any): Promise<any> {
    return Fetch({url: Api.scanCodeHostCode, data: data, method: 'POST', showLoading: true})
}
export function cabinetOrderPaid(data: any): Promise<any> {
    return Fetch({url: Api.cabinetOrderPaid, data: data, method: 'POST', showLoading: true})
}

export function cabinetOrderOpen(data: any): Promise<any> {
    return Fetch({url: Api.cabinetOrderOpen, data: data, method: 'POST', showLoading: true})
}

export function getMobileList(data: any): Promise<any> {
    return Fetch({url: Api.getMobileList, data: data, method: 'POST', showLoading: true})
}

export function sendSms(data: any): Promise<any> {
    return Fetch({url: Api.sendSms, data: data, method: 'POST', showLoading: true})
}

export function addMobile(data: any): Promise<any> {
    return Fetch({url: Api.addMobile, data: data, method: 'POST', showLoading: true})
}

export function removeMobile(data: any): Promise<any> {
    return Fetch({url: Api.removeMobile, data: data, method: 'POST', showLoading: true})
}

export function orderByCode(data: any): Promise<any> {
    return Fetch({url: Api.orderByCode, data: data, method: 'POST', showLoading: true})
}

export function testCheckCode(data: any): Promise<any> {
    return Fetch({url: Api.testCheckCode, data: data, method: 'POST', showLoading: true})
}

export function getCheckCode(data: any): Promise<any> {
    return Fetch({url: Api.getCheckCode, data: data, method: 'POST', showLoading: true})
}

export function bannerList(data: any): Promise<any> {
    return Fetch({url: Api.bannerList, data: data, method: 'POST', showLoading: true})
}

export function wxRecharge(data: any): Promise<any> {
    return Fetch({url: Api.wxRecharge, data: data, method: 'POST', showLoading: true})
}

export function openDoor(data: any): Promise<any> {
    return Fetch({url: Api.openDoor, data: data, method: 'POST', showLoading: true})
}

export function getOrderListInShop(data: cabinetCodeDto): Promise<any> {
    return Fetch({url: Api.orderListInShop, data: data, method: 'POST', showLoading: true})
}

/**
 * 获取账单列表数据
 * @param data - 包含获取账单列表所需参数的对象，类型为 billListDto
 * @returns 返回一个 Promise，解析为服务器响应的数据
 */
export function getBillList(data: billListDto): Promise<any> {
    // 发起 POST 请求获取账单列表，显示加载状态
    return Fetch({url: Api.billList, data: data, method: 'POST', showLoading: true})
}
