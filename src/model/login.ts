import {Fetch} from '@/utils/core/fetch'
import Api from '@/utils/api'
import {addressDto, bindPhoneDto, loginDto, realNameDto, userDto,bindPhoneZfDto} from '@/utils/types'

export function login(data: loginDto): Promise<userDto> {
    return Fetch({url: Api.login, data: data, method: 'POST', showLoading: true})
}
export function getCodeDate(data: any): Promise<any> {
    return Fetch({url: Api.qrCodeDate, data: data, method: 'POST', showLoading: true})
}

export function mockLogin(data: any): Promise<userDto> {
    return Fetch({url: Api.mock, data: data, method: 'POST', showLoading: true})
}

export function getUserInfo(): Promise<userDto> {
    return Fetch({url: Api.userInfo, data: {}, method: 'POST', showLoading: true})
}

export function wempBindPhone(data: bindPhoneDto | bindPhoneZfDto ): Promise<any> {
    return Fetch({url: Api.wempBindPhone, data: data, method: 'POST', showLoading: true})
}

export function bindGzh(data: any): Promise<any> {
    return Fetch({url: Api.bindGzh, data: data, method: 'POST', showLoading: true})
}

export function realNameAuth(data: realNameDto): Promise<any> {
    return Fetch({url: Api.authRealName, data: data, method: 'POST', showLoading: true})
}
