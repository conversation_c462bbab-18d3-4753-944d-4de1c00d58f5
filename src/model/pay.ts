import Api from '@/utils/api'
import {outboundDto, payDto} from "@/utils/types";
import {Fetch} from "@/utils/core/fetch";

export function getPayParams(info: payDto) {
    return Fetch({url: Api.payParams, data: info, method: 'POST', showLoading: true})
}
export function getOrderIsPay(info: outboundDto) {
    return Fetch({url: Api.orderIsPay, data: info, method: 'POST', showLoading: true})
}

export function orderCancel(info: outboundDto) {
    return Fetch({url: Api.orderCancel, data: info, method: 'POST', showLoading: true})
}
