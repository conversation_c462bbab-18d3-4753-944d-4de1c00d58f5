import {Fetch} from "@/utils/core/fetch";
import Api from "@/utils/api";
import {addressDto, addressInfo} from "@/utils/types";

export function getDefaultAddress(): Promise<addressInfo> {
    return Fetch({url: Api.getDefaultAddress, data: {}, method: 'POST', showLoading: false})
}

export function addressPage(data: any): Promise<any> {
    return Fetch({url: Api.addressList, data: data, method: 'POST', showLoading: false})
}

export function saveAddress(data: addressDto): Promise<any> {
    return Fetch({url: Api.saveAddress, data: data, method: 'POST', showLoading: true})
}

export function extractExpress(data: any): Promise<any> {
    return Fetch({url: Api.extractExpress, data: data, method: 'POST', showLoading: true})
}

export function defaultAddress(data: any): Promise<any> {
    return Fetch({url: Api.defaultAddress, data: data, method: 'POST', showLoading: true})
}

export function deleteAddress(data: any): Promise<any> {
    return Fetch({url: Api.deleteAddress, data: data, method: 'POST', showLoading: true})
}

export function usableSummary(data: any): Promise<any> {
    return Fetch({url: Api.usableSummary, data: data, method: 'POST', showLoading: true})
}
export function saveOrder(data: any): Promise<any> {
    return Fetch({url: Api.createOrder, data: data, method: 'POST', showLoading: true})
}
export function cancelOrder(data: any): Promise<any> {
    return Fetch({url: Api.cancelOrder, data: data, method: 'POST', showLoading: true})
}
export function getOrderDetail(data: any): Promise<any> {
    return Fetch({url: Api.orderDetail, data: data, method: 'POST', showLoading: true})
}
export function createPayOrder(data: any): Promise<any> {
    return Fetch({url: Api.createPayOrder, data: data, method: 'POST', showLoading: true})
}
export function checkPayOrder(data: any): Promise<any> {
    return Fetch({url: Api.checkPayOrder, data: data, method: 'POST', showLoading: true})
}
export function openSendDoor(data: any): Promise<any> {
    return Fetch({url: Api.openSendDoor, data: data, method: 'POST', showLoading: true})
}
export function refundList(data: any): Promise<any> {
    return Fetch({url: Api.refundList, data: data, method: 'POST', showLoading: true})
}
export function openBoxByOrder(data: any): Promise<any> {
    return Fetch({url: Api.openBoxByOrder, data: data, method: 'POST', showLoading: true})
}