<view class="container">
    <view class="item">
        <uni-forms ref="subForm" :model-value="applyForm">
            <view class="title">
                您希望铺设快递柜的具体位置
            </view>
<!--            <view class="content">省市区</view>-->
<!--            <uni-forms-item name="pca">-->
<!--                <view @click="locationOrGoSetting">-->
<!--                    <input-widget padding="0" :item-height="110" placeholder="请选择省市区" v-model="applyForm.pca"-->
<!--                                  :show-clear="true" type="select">-->
<!--                        &lt;!&ndash;                    <view slot="btn" class="slot-btn" @click="locationOrGoSetting">&ndash;&gt;-->
<!--                        <view slot="btn" class="slot-btn">-->
<!--                            <text class="iconfont icon-location"></text>-->
<!--                        </view>-->
<!--                    </input-widget>-->
<!--                </view>-->
<!--            </uni-forms-item>-->
            <view class="content">详细地址</view>
            <uni-forms-item name="address">
                <input-widget padding="0" :item-height="110" placeholder="请输入详细地址" v-model="applyForm.address"
                              :show-clear="true">
                    <view slot="btn" class="slot-btn" @click="locationOrGoSetting">
                        <text class="iconfont icon-location"></text>
                    </view>
                </input-widget>
            </uni-forms-item>
            <view class="title mt20">
                您希望铺设的小区
            </view>
            <view class="content">小区名字</view>
            <uni-forms-item name="applyVillage">
                <input-widget padding="0" :item-height="110" placeholder="请输入小区名字" v-model="applyForm.applyVillage"
                              :show-clear="true">
                </input-widget>
            </uni-forms-item>
            <view class="title mt20">
                您想自己铺设还是交给他人
            </view>
            <view class="radio-groups">
                <view class="radio-item" v-for="(item, index) in applyReasonList" :key="index"
                      @click="setApplyReason(item.name)">
                    <img :src="getImage(item.name === applyForm.applyReason ? 'select': 'unselect')" alt="">
                    <text>{{ item.name }}</text>
                </view>
            </view>
        </uni-forms>
        <view class="bottom">
            <button class="btn auth"
                    @click="addApply">
                <text>提交申请</text>
            </button>
        </view>
    </view>
</view>
