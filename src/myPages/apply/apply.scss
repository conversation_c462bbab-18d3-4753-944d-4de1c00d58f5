.container {
  position: relative;
  width: 750upx;

  .item {
    margin-top: 30upx;
    margin-left: 30upx;
    margin-right: 30upx;
    width: 690upx;
    padding: 30upx;
    box-sizing: border-box;
    background: #FFFFFF;
    border-radius: 10upx;

    .title {
      font-size: 30upx;
      font-weight: 500;
      color: #000000;
    }

    .content {
      font-size: 24upx;
      font-weight: 400;
      color: #585858;
    }
    .mt20{
      margin-top: 20upx;
    }
  }


  .radio-groups{
    .radio-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 64upx;
      image{
        width: 34upx;
        height: 34upx;
        margin-right: 24upx;
      }
      text{
        font-size: 28upx;
        font-weight: 400;
        color: #333333;
      }
    }
  }
}