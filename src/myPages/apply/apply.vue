<script lang="ts">
import 'reflect-metadata';
import {Component, Prop, Vue} from 'vue-property-decorator';
import UniForms from '@/components/uni-forms/uni-forms.vue';
import UniFormsItem from "@/components/uni-forms-item/uni-forms-item.vue";
import InputWidget from "@/components/input-widget/index.vue";
import {ImageMixins} from "@/mixins/ImageMixins";
import {Modal} from '@/utils';
import Config from "@/utils/config";
import {submitCabinetLocationApply} from "@/model/cabinet";

var QQMapWX = require('@/utils/core/qqmap-wx-jssdk.min.js');
var qqmapsdk = new QQMapWX({
  key: Config.QQMAP_KEY
});
@Component({
  name: 'Apply',
  components: {UniForms, UniFormsItem, InputWidget}
})
export default class Apply extends ImageMixins {
  $refs!: {
    subForm: HTMLFormElement
  }
  private locationAuth: boolean = false
  private applyForm: any = {
    pca: '',
    provinceName: '',
    cityName: '',
    areaName: '',
    address: '',
    applyVillage: '',
    longitude: 0,
    latitude: 0,
    applyReason: '自己有资源铺设'
  }
  private rules: any = {
    pca: {
      rules: [{required: true, errorMessage: '请选择省市区'}]
    },
    address: {
      rules: [{required: true, errorMessage: '请输入详细地址'}]
    },
    district: {
      rules: [{required: true, errorMessage: '请输入小区名字'}]
    },
    validateTrigger: 'submit'
  }
  private applyReasonList: any = [
    {
      name: '自己有资源铺设',
      value: 1
    },
    {
      name: '我是业主，让物业铺设',
      value: 2
    },
    {
      name: '我是物业，让他人铺设',
      value: 3
    },
  ]

  locationOrGoSetting() {
    if (this.locationAuth) {
      // this.getLocation()
      this.chooseLocation()
    } else {
      uni.openSetting({})
    }
  }

  chooseLocation() {
    var that = this
    uni.chooseLocation({
      success: function (res: any) {
        qqmapsdk.reverseGeocoder({
          location: {
            latitude: res.latitude,
            longitude: res.longitude
          },
          success: function (res_: any) {
            let province = res_.result.ad_info.province
            let city = res_.result.ad_info.city
            let area = res_.result.ad_info.district
            let applyVillage = res_.result.address_reference.landmark_l2.title
            let address = res_.result.address
            let lat = res_.result.location.lat
            let lng = res_.result.location.lng
            that.applyForm.provinceName = province
            that.applyForm.cityName = city
            that.applyForm.areaName = area
            that.applyForm.latitude = lat
            that.applyForm.longitude = lng
            that.applyForm.applyVillage = applyVillage
            that.applyForm.address = address
            that.applyForm.pca = province + '/' + city + '/' + area
          },
          fail: function (err: any) {
          },
          complete: function (res: any) {
          }
        });
      },
      fail: function () {

      }
    })
  }

  getLocation() {
    var that = this
    uni.getLocation({
      type: 'gcj02',
      success: function (res) {
        qqmapsdk.reverseGeocoder({
          location: {
            latitude: res.latitude,
            longitude: res.longitude
          },
          success: function (res_: any) {
            let province = res_.result.ad_info.province
            let city = res_.result.ad_info.city
            // that.city = city
            that.applyForm.address = res_.result.address
          },
          fail: function (err: any) {
          },
          complete: function (res: any) {
          }
        });
      },
      fail: function (err) {
      }
    })
  }

  setApplyReason(value: number) {
    this.applyForm.applyReason = value
  }

  async addApply() {
    if (this.applyForm.provinceName === '' || this.applyForm.cityName === '' || this.applyForm.areaName === '') {
      Modal.toast('请选择省市区')
      return false
    }
    if (this.applyForm.applyVillage === '') {
      Modal.toast('请填写小区名字')
      return false
    }
    if (this.applyForm.applyReason === '') {
      Modal.toast('请选择申请理由')
      return false
    }
    let info = Object.assign({}, {...this.applyForm})
    delete info.pca
    delete info.address
    const res = await submitCabinetLocationApply(info)
    if (res) {
      await Modal.alert('您的申请已提交,客服人员会在接到工单后第一时间与您联系,请保持电话通畅，祝您生活愉快', '申请成功')
      uni.navigateBack({})
    }
  }

  onLoad() {
    this.$refs.subForm.setRules(this.rules)
  }

  onShow() {
    const that = this
    uni.authorize({
      scope: 'scope.userLocation',
      success() {
        that.locationAuth = true
        // that.getLocation()
      },
      fail() {
        that.locationAuth = false
      }
    })
  }
}
</script>

<template src="./apply.html"></template>
<style src="./apply.scss" lang="scss" scoped></style>
