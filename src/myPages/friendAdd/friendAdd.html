<view class="container">
  <view class="item">
    <view class="title">请绑定您亲友的手机号</view>
    <uni-forms ref="subForm" :modelValue="friendItem">
      <uni-forms-item name="mobile">
        <input-widget
          placeholder="请输入手机号"
          v-model="friendItem.mobile"
          label="手机号"
          :show-clear="true"
        ></input-widget>
      </uni-forms-item>
      <uni-forms-item name="captcha">
        <input-widget
          placeholder="请输入验证码"
          v-model="friendItem.captcha"
          label="验证码"
          :show-clear="true"
        >
          <view slot="btn" @click="getCaptcha">
            <view class="captcha-btn"
              >{{ timerCount < 60 ? timerCount + 's':'获取验证码'}}</view
            >
          </view>
        </input-widget>
      </uni-forms-item>
      <view class="title">设置昵称</view>
      <view class="content">{{ `给您的好友设置个昵称吧` }}</view>
      <uni-forms-item name="alias">
        <input-widget
          placeholder="请输入昵称"
          v-model="friendItem.alias"
          :show-clear="true"
        >
        </input-widget>
      </uni-forms-item>
      <!--<view class="content" style="margin-top: 30upx">{{ `猜你想取` }}</view>-->
      <!--<view class="tag-list">-->
      <!--    <view class="tag-item" v-for="(item, index) in tagList" :key="index" :class="{'active': selectTag === item.name}" @click="changeTag(item.name)">-->
      <!--        <text>{{item.name}}</text>-->
      <!--    </view>-->
      <!--</view>-->
      <view class="btn_align">
        <button class="btn auth" @click="addNewFriend">
          <text>添加</text>
        </button>
      </view>
    </uni-forms>

  </view>
</view>
