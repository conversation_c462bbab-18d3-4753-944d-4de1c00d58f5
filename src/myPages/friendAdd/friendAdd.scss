.container {
  width: 750upx;
  position: relative;

  .item {
    margin-top: 30upx;
    margin-left: 30upx;
    margin-right: 30upx;
    width: 690upx;
    padding: 30upx;
    box-sizing: border-box;
    background: #FFFFFF;
    border-radius: 10upx;

    .title {
      margin-top: 30upx;
      font-size: 30upx;
      font-weight: 500;
      color: #000000;
    }

    .content {
      font-size: 24upx;
      font-weight: 400;
      color: #585858;
    }

    .captcha-btn {
      font-size: 26upx;
      font-weight: 400;
      color: $config-color;
    }
  }

  .tag-list {
    display: flex;
    flex-direction: row;
    height: 72upx;
    align-items: center;

    .tag-item {
      display: flex;
      justify-content: center;
      align-items: center;
      background: #EEEEEE;
      border-radius: 22px;
      margin-right: 20upx;
      color: #858585;
      height: 44upx;
      min-width: 82upx;

      text {
        font-size: 24upx;
        font-weight: 400;
      }
    }

    .active {
      color: #ffffff;
      background-color: $config-color;
    }
  }

  .btn_align{
    text-align: center;
    width: 100%;
  }

  .btn {
    width: 400upx;
    height: 74upx;
    margin-top: 50upx;
    line-height: 74upx;
    box-sizing: border-box;
    border-radius: 45upx;
    text-align: center;
    font-size: $uni-font-size-sm;
    display: inline-block;
  }

}

.auth {
  color: #ffffff;
  background-color: $config-color;
  margin-bottom: 40upx;
  margin-top: 10upx;
}
