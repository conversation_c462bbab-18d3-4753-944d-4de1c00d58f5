<script lang="ts">
import 'reflect-metadata';
import {Component, Prop, Vue} from 'vue-property-decorator';
import UniForms from '@/components/uni-forms/uni-forms.vue';
import UniFormsItem from "@/components/uni-forms-item/uni-forms-item.vue";
import InputWidget from "@/components/input-widget/index.vue";
import {Modal} from '@/utils';
import {addMobile, sendSms} from "@/model/cabinet";

@Component({
  name: 'FriendAdd',
  components: {UniForms, UniFormsItem, InputWidget}
})
export default class FriendAdd extends Vue {
  $refs!: {
    subForm: HTMLFormElement
  }
  private friendItem: any = {
    mobile: '',
    captcha: '',
    alias: ''
  }
  private timer: any = null
  private timerCount: number = 60
  private selectTag: string = ''

  private rules: any = {
    mobile: {
      rules: [{required: true, errorMessage: '请输入手机号'}, {
        validateFunction: function (rule: any, value: string, data: any, callback: (arg0: string) => void) {
          var regIdNo = /^1[3456789]\d{9}$/
          if (!regIdNo.test(value)) {
            callback('手机号格式错误')
          }
          return true
        }
      }]
    },
    captcha: {
      rules: [{required: true, errorMessage: '请输入验证码'}, {
        validateFunction: function (rule: any, value: string, data: any, callback: (arg0: string) => void) {
          var regIdNo = /(^\d{4,8}$)/
          if (!regIdNo.test(value)) {
            callback('验证码格式错误')
          }
          return true
        }
      }]
    },
    alias: {
      rules: [{required: true, errorMessage: '请输入昵称'}]
    },
  }

  onLoad(e: any) {
    if (e.item != null) {
      this.friendItem = JSON.parse(e.item)
    }
  }
  onReady() {
		//onReady 为uni-app支持的生命周期之一
    	this.$refs.subForm.setRules(this.rules)
}


 async getCaptcha() {
    const mobile = this.friendItem.mobile
    if (this.timerCount === 60) {
      if (mobile != null && mobile != '') {
        var regIdNo = /^1[3456789]\d{9}$/
        if (!regIdNo.test(mobile)) {
          Modal.toast('手机号格式有误')
          return false;
        } else {
          let data = {
            loginName: mobile,
            smsTplCode: 'CAPTCHA_SMS'
          }
          let res = await sendSms(data)
          if (res) {
            this.timer = null
            this.timer = setInterval(() => {
              if (this.timerCount > 0) {
                this.timerCount--
              } else {
                clearInterval(this.timer)
                this.timerCount = 60
              }
            }, 1000)
          }
        }
      } else {
        Modal.toast('请输入手机号')
      }
    }
  }

  async addNewFriend() {
  try {
    const res = await this.$refs.subForm.validate();
    const data = {
      checkCode: res.captcha,
      mobile: res.mobile,
      alias: res.alias
    };
    
    const result = await addMobile(data);
    if (result) {
      uni.navigateBack({ delta: 1 });
    }
  } catch (error) {
    console.error('添加好友失败:', error);
    uni.showToast({
      title: '添加失败，请重试',
      icon: 'none'
    });
  }
}

  changeTag(tag: string) {
    this.selectTag = tag
  }

  onHide() {
    this.timer = null
    clearInterval(this.timer)
  }

}
</script>

<template src="./friendAdd.html"></template>
<style src="./friendAdd.scss" lang="scss" scoped></style>
