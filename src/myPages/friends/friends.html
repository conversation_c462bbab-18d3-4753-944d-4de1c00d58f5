<view class="container">
    <scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore">
        <view v-if="friendList.length > 0">
            <view v-for="(item, index) in friendList" :key="index" class="list-item">
                <img :src="getUrl(item.url)">
                <view class="middle">
                    <view class="mobile">
                        {{ item.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}
                    </view>
                    <view class="f-tag">
                        <view class="text">{{ item.alias }}</view>
                    </view>
                </view>
                <view class="right" @click="delMobile(item)">删除</view>
            </view>
        </view>
        <view class="item" v-else>
            <img :src="getImage('friendCenter')" alt="">
            <view class="text" style="margin-top: 30upx">
                有亲友需要帮忙取包裹？！
            </view>
            <view class="text" style="margin-top: 10upx">
                点击添加手机号，即可帮TA代取哦!
            </view>
        </view>
    </scroll-view>
    <view class="bottom">
        <button class="btn auth"
                @click="goFriendAdd">
            <text>添加手机号</text>
        </button>
    </view>
</view>
