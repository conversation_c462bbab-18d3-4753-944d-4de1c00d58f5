.container {
  position: relative;
  width: 750upx;
  height: 100%;
  height: 100vh;
}

.item {
  width: 690upx;
  height: 505upx;
  background: #FFFFFF;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  image {
    width: 236upx;
    height: 234upx;
  }

  .text {
    font-size: 30upx;
    font-weight: 400;
    color: #000000;
    line-height: 48upx;

  }
}

.bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  text-align: center;
}

.btn {
  width: 690upx;
  height: 90upx;
  line-height: 90upx;
  border-radius: 45upx;
  text-align: center;
  font-size: $uni-font-size-sm;
  display: inline-block;
}

.auth {
  color: #ffffff;
  background-color: $config-color;
  margin-bottom: 40upx;
  margin-top: 10upx;
}

.scroll-view {
  position: absolute;
  top: 0upx;
  left: 0;
  right: 0;
  bottom: 140upx;
  padding-top: 30upx;
  padding-left: 30upx;
  padding-right: 30upx;
  box-sizing: border-box;

  .list-item {
    display: flex;
    flex-direction: row;
    width: 690upx;
    height: 160upx;
    box-sizing: border-box;
    align-items: center;
    background: #FFFFFF;
    border-radius: 10px;
    padding: 30upx;
    margin-bottom: 30upx;

    image {
      width: 85upx;
      height: 85upx;
    }

    .middle {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-left: 20upx;

      .mobile {
        font-size: 36upx;
        font-weight: 400;
        color: #000000;
        line-height: 48upx;
      }

      .f-tag {
        width: 81upx;
        height: 44upx;
        background: #EEEEEE;
        border-radius: 22px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 18upx;
        .text{
          font-size: 24upx;
          font-weight: 400;
          color: #858585;
        }
      }
    }

    .right {
      color: $config-color;
    }
  }
}