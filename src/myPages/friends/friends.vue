<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import Utils from "../../utils/core/utils";
import {ImageMixins} from "@/mixins/ImageMixins";
import {getMobileList, removeMobile} from "@/model/cabinet";
import {Modal} from "@/utils";

@Component({
  name: 'Friends'
})
export default class Friends extends ImageMixins {
  private friendList: any = []

  async getList() {
    let data = {}
    this.friendList = await getMobileList(data)
  }

  async onPullDownRefresh() {
    await this.getList()
    uni.stopPullDownRefresh()
  }

  onShow() {
    this.getList()
  }

  async delMobile(item: any) {
    await Modal.confirm('是否删除手机号', '温馨提示', '取消', '确认')
    let data = {
      id: item.id
    }
    let res = removeMobile(data)
    if (await res) {
      await this.getList()
    }
  }

  goFriendAdd() {
    let url: string = '/myPages/friendAdd/friendAdd'
    uni.navigateTo({url: url})
  }

  getUrl(url: string) {
    if (url == '' || url == null) {
      return Utils.getImage('logo')
    }
  }

  loadMore() {

  }
}
</script>

<template src="./friends.html"></template>
<style src="./friends.scss" lang="scss" scoped></style>
