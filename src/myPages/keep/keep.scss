.container {
  position: relative;
  width: 750upx;
  height: 100vh;
  .space-two {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  .item{
    background-color: #ffffff;
    height: 90upx;
    margin-top: 30upx;
    padding-left: 30upx;
    padding-right: 30upx;
  }
  .switch {
    scale: 0.7;
  }
}

.bottom{
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  text-align: center;
}
.btn{
  width: 690upx;
  height: 90upx;
  line-height: 90upx;
  border-radius: 45upx;
  text-align: center;
  font-size: $uni-font-size-sm;
  display: inline-block;
}
.auth{
  color: #ffffff;
  background-color: $config-color;
  margin-bottom: 40upx;
  margin-top: 10upx;
}

