<script lang="ts">
import 'reflect-metadata';
import {Component, Vue, Watch} from 'vue-property-decorator';
import {C<PERSON>, Modal} from '@/utils';
import {setCanInbound} from "@/model/cabinet";
import {syncUser} from '@/utils/core/uni';
import config from "@/utils/config";

@Component({
  name: 'Keep'
})
export default class Keep extends Vue {
  private isKeep: boolean = false
  private configColor=config.CONFIG_COLOR

  switchChange(e: any) {
    this.isKeep = e.detail.value
  }

  @Watch('isKeep')
  async isKeepChange(val: boolean) {
    if (!val) {
      Modal.confirm(
          '开关关闭后，您的包裹将不再入柜,是否确认关闭？',
          '温馨提示',
          '取消',
          '确认关闭'
      ).then(() => {
        this.submit()
        uni.navigateBack({})
      }).catch((e) => {
        this.isKeep = true
      })
    }
  }

  async submit() {
    const res = await setCanInbound({canInbound: this.isKeep ? 1 : 0})
    if (res) {
      await syncUser()
      Modal.toast(this.isKeep ? '开启成功' : '关闭成功')
    }
  }

  onLoad() {
    const user = Cache.getItem('user')
    this.isKeep = user.canInbound === 1
  }
}
</script>

<template src="./keep.html"></template>
<style src="./keep.scss" lang="scss" scoped></style>
