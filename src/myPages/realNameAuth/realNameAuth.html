<view class="container">
    <view class="info" v-if="!canEdit">
        <view class="space-two border-bottom item-height">
            <view class="label">姓名</view>
            <view class="label">{{ user.realName }}</view>
        </view>
        <view class="space-two item-height">
            <view class="label">身份证</view>
            <view class="label">{{ user.idNumber }}</view>
        </view>
        <view class="bottom">
            <button class="btn auth" hover-class="btn-hover" :class="{'active': true}"
                    @click="editForm">
                <text>修改实名信息</text>
            </button>
        </view>
    </view>
    <uni-forms v-else ref="subForm" :model-value="authForm">
        <view class="input-info">
            <uni-forms-item labe="" name="realName">
                <input-widget placeholder="请输入您的姓名" :item-height="110" :is-required="true" padding="0"
                              v-model="authForm.realName"
                              :show-clear="true"></input-widget>
            </uni-forms-item>
            <uni-forms-item labe="" name="idNumber">
                <input-widget placeholder="请输入您的身份证号码" :item-height="110" padding="0" :is-required="true"
                              v-model="authForm.idNumber"
                              :show-clear="true"></input-widget>
            </uni-forms-item>

        </view>
        <view class="bottom">
            <button class="btn auth" hover-class="btn-hover" :class="{'active': formValidate}"
                    @click="submitRealAuth">
                <text>提交认证</text>
            </button>
        </view>
    </uni-forms>
</view>