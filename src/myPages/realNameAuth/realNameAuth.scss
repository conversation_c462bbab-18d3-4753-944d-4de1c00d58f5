.container{
  position: relative;
  width: 750upx;
  min-height: 100vh;
  .info{
    background-color: #ffffff;
    box-sizing: border-box;
    padding: 0 30upx;
    margin-top: 30upx;
    .label{
      font-size: 30upx;
      font-weight: 400;
      color: #585858;
    }

    .item-height{
      height: 96upx;
    }
    .border-bottom {
      border-bottom: 1px solid #EEEEED;
    }
  }

  .input-info{
    margin-top: 30upx;
    padding: 0 30upx;
    background-color: #ffffff;
  }
}
.space-two {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.bottom{
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  text-align: center;
}
.btn{
  width: 690upx;
  height: 90upx;
  line-height: 90upx;
  border-radius: 45upx;
  text-align: center;
  font-size: $uni-font-size-sm;
  display: inline-block;
}
.auth{
  color: #ffffff;
  opacity: 0.5;
  //background-color: $config-color;
  margin-bottom: 40upx;
  margin-top: 10upx;
}

.active {
  background-color: $config-color;
}