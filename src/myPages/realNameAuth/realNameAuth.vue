<script lang="ts">
import 'reflect-metadata';
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';

import InputWidget from '@/components/input-widget/index.vue'
import UniForms from '@/components/uni-forms/uni-forms.vue'
import UniFormsItem from '@/components/uni-forms-item/uni-forms-item.vue'
import {Cache, Modal} from "@/utils";
import {realNameAuth} from "@/model/login";
import {realNameDto} from "@/utils/types";
import {syncUser} from "@/utils/core/uni";

@Component({
  name: 'RealNameAuth', components: {InputWidget, UniForms, UniFormsItem}
})
export default class RealNameAuth extends Vue {
  $refs!: {
    subForm: HTMLFormElement
  }
  private canEdit: boolean = true
  private cabinetInfo: string = ''
  private user: any = {
    hasReal: 0,
    realName: '',
    idNumber: ''
  }
  private authForm: realNameDto = {
    realName: '',
    idNumber: ''
  }
  private rules: any = {
    realName: {
      rules: [{required: true, errorMessage: '请输入姓名', trigger: ['blur', 'change']}, {
        validateFunction: function (rule: any, value: string, data: any, callback: (arg0: string) => void) {
          const regName = /^[\u4e00-\u9fa5]{2,10}$/
          if (!regName.test(value)) {
            callback('姓名填写有误')
          }
          return true
        }
      }]
    },
    idNumber: {
      rules: [{required: true, errorMessage: '请输入身份证号码'}, {
        validateFunction: function (rule: any, value: string, data: any, callback: (arg0: string) => void) {
          const regIdNo = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
          if (!regIdNo.test(value)) {
            callback('身份证号码填写有误')
          }
          return true
        }
      }]
    }
  }
  private formValidate: boolean = false

  @Watch('authForm.idNumber')
  onIdNoChanged(val: string, oldVal: string) {
    // this.$refs.subForm.validateField('idNumber')
    this.formValidate = this.checkFormValidate()
  }

  @Watch('authForm.realName')
  onRealNameChanged(val: string, oldVal: string) {
    // this.$refs.subForm.validateField('realName')
    this.formValidate = this.checkFormValidate()
  }

  startFace() {
    // @ts-ignore
    wx.startFacialRecognitionVerify({
      name: this.authForm.realName,
      idCardNumber: this.authForm.idNumber,
      checkAliveType: 2,
      success: (res: any) => {
        console.log(res);
      },
      fail: (err: any) => {
        Modal.toast('请保持光线充足，面部正对手机，且无遮挡')
      }
    })

  }

  async authUser() {
    const result = await realNameAuth(this.authForm)
    if (result) {
      await syncUser()
      this.getUser()
      this.canEdit = false
      Modal.toast('实名认证成功')
      if (this.cabinetInfo) {
        uni.redirectTo({url: '/homePages/send/sendForm/sendForm?cabinetInfo=' + this.cabinetInfo})
      }
    }
  }

  async submitRealAuth() {
    if (this.formValidate) {
      this.$refs.subForm.validate().then(async (res: any) => {
        if (res) {
          this.authUser()
          // // @ts-ignore
          // wx.checkIsSupportFacialRecognition({
          //   checkAliveType: 2,
          //   success: (res: any) => {
          //     if (res.errMsg === "checkIsSupportFacialRecognition:ok") {
          //       this.startFace()
          //     }
          //   },
          //   fail: (res: any) => {
          //     console.log('fail', res);
          //   }
          // })
        }
      }).catch((err: any) => {
        console.log(err)
      })
    } else {
      Modal.toast('请将信息填写完成后提交')
    }
  }

  checkFormValidate() {
    return this.authForm.idNumber != '' && this.authForm.idNumber != null && this.authForm.realName != '' && this.authForm.realName != null
  }

  editForm() {
    this.canEdit = true
  }

  getUser() {
    const user = Cache.getItem('user')
    if (user != null) {
      this.user = user
      this.canEdit = user.hasReal === 0
      this.authForm = {realName: user.realName, idNumber: user.idNumber}
    }
  }

  onLoad(e: any) {
    if (e.cabinetInfo) {
      this.cabinetInfo = e.cabinetInfo
    }
    this.formValidate = this.checkFormValidate()
    this.getUser()
    this.$refs.subForm.setRules(this.rules)
  }
}
</script>

<template src="./realNameAuth.html"></template>
<style src="./realNameAuth.scss" lang="scss" scoped></style>
