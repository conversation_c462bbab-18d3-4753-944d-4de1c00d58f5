<view class="container">
    <view class="info">
        <view class="space-two border-bottom" style="height: 120upx">
            <view class="label">头像</view>
            <view class="right" @click.capture="getUserLogo">
                <text>点击更新微信昵称/头像</text>
                <img :src="getUserAvatar()" alt="" class="image">
            </view>
            <!--            <open-data type="userAvatarUrl" class="image"></open-data>-->
        </view>
        <view class="space-two border-bottom item-height" >
            <view class="label">用户名</view>
            <view class="value">{{user.nickname}}</view>
<!--            <open-data type="userNickName"></open-data>-->
        </view>
        <view class="space-two border-bottom item-height">
            <view class="label">手机号</view>
<!--            <button open-type="getPhoneNumber" @getphonenumber="goPhoneBind"-->
<!--                    class="mobile-button">-->
                <view class="value">{{user.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}}</view>
<!--            </button>-->
        </view>
        <view class="space-two item-height" @click="toRealNameAuth">
            <view class="label">实名认证</view>
            <view class="right-info">
                <view class="value" :class="{'ppp':user.hasReal === 0}">{{user.hasReal === 1? '已实名': '未实名'}}</view>
                <img src="../../static/img/right-arrow.png" alt="" style="width: 20upx; height: 30upx;margin-left: 12upx">
            </view>
        </view>
    </view>
</view>
