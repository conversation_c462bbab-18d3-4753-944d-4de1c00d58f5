.container {
  width: 750 upx;
  position: relative;

  .info {
    width: 690upx;
    height: 450upx;
    background: #FFFFFF;
    border-radius: 10px;
    padding: 30upx 30upx 0 30upx;
    margin-left: 30upx;
    margin-top: 30upx;
    box-sizing: border-box;
    .right{
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: flex-end;
      text{
        font-size: 24upx;
        font-weight: 400;
        color: #999999;
        line-height: 48upx;
      }
    }
    .image{
      margin-left: 30upx;
      width: 114upx;
      height: 114upx;
      border-radius: 70upx;
    }
    .mobile-button{
      color: #000000;
      //display: contents;
      margin-right: 0;
      padding: 0;
      line-height: 1.2;
      background-color: #ffffff;
      font-size: 28upx;
    }
    .mobile-button:after{
      border: none;
      color: #000000;
    }
    .label{
      font-size: 30upx;
      font-weight: 400;
      color: #585858;
    }
    .value {
      font-size: 30upx;
      font-weight: 400;
      color: #000000;
    }

    .ppp{
      color: $config-color;
    }
    .image{
      width: 84upx;
      height: 84upx;
    }
    .border-bottom {
      border-bottom: 1px solid #EEEEED;
    }
    .item-height{
      height: 96upx;
    }
  }
}
.space-two {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.right-info {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}