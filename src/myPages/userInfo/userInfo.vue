<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import {Cache, Modal} from '@/utils'
import {ImageMixins} from "@/mixins/ImageMixins";
import {bindPhone, loginByWechat} from "@/utils/core/uni";
import {getUserInfo} from '@/model/login';
import {wechatPhoneDto} from "@/utils/types";

@Component({
  name: 'Userinfo'
})
export default class Userinfo extends ImageMixins {
  private user: any = {
    // realName: '张三',
    // hasReal: 0,
    userName: '微信用户',
    avatar: this.getImage('logo'),
    phone: ''
  }

  getUserAvatar() {
    if (this.user.avatar) {
      return this.user.avatar
    }
    return this.getImage('logo')
  }

  toRealNameAuth() {
    uni.navigateTo({url: '/myPages/realNameAuth/realNameAuth'})
  }

  async getUserLogo() {
    const res: any = await loginByWechat();
    if (res != null && res.isLogin) {
      await this.getUserInfo()
      Modal.toast('头像更新成功')
    }
  }

  async getUserInfo() {
    const user: any = await getUserInfo();
    this.user = user
    Cache.setItem('user', user)
  }

  async goPhoneBind(data: wechatPhoneDto) {
    if (data.detail.errMsg === 'getPhoneNumber:ok') {
      const res: any = await bindPhone(data)
      if (res != null) {
        Modal.toast('手机号授权成功')
        await this.getUserInfo()
      }
      // bindWechatPhone(data, false)
      // Modal.toast('手机号授权成功')
      // this.getUserInfo()
    }
  }

  onShow() {
    const user = Cache.getItem('user')
    this.user = user
  }

  onLoad() {
  }
  async onPullDownRefresh() {
    uni.stopPullDownRefresh()
  }
}
</script>

<template src="./userInfo.html"></template>
<style src="./userInfo.scss" lang="scss" scoped></style>
