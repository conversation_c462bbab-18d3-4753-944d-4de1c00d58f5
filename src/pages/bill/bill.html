<view>
    <!-- #ifdef MP-WEIXIN -->
    <u-navbar :bgColor="backgroundColor" safeAreaInsetTop :catchtouchmove="true" fixed leftIconColor='#FFFFFF'
              placeholder title="待取列表" titleStyle="color:#FFFFFF" @left-click="backHome" >
    </u-navbar>
    <!-- #endif -->
     <!-- #ifdef MP-ALIPAY -->
    <view class="navber" :style="{'backgroundColor':backgroundColor}" >
        <view class="navber-left">
            <view class="navber-left-icon" @click="backHome">
                <u-icon color="#FFFFFF" name="arrow-left" size="25px"></u-icon>
            </view>
        </view>
        <view class="navber-title">
            待取列表
        </view>
    </view>
    <view style="margin-top: 100upx;"> </view>
     <!-- #endif -->
 
    <view class="container">
        <view class="container-code padding030">
            <view class="title">{{ locationName }}</view>
            <view class="input">
                <input-widget v-model="valArr.val"  :fontSize="22" :item-height="80" :noBorder="true"  :noBgColor="false" :show-clear="false"
                              class="input-style"
                              inputType="number"
                              placeholder="请输入取件码" @input="codeNum">
                </input-widget>
            </view>
        </view>
        <scroll-view class="scroll-view" lower-threshold="200" scroll-y>
            <view v-if="billList.length>0" class="scroll-view-list">
                <view v-for="(item, index) in billList" :key="index">
                    <view class="item">
                        <view class="item-left">
                            <view :class="index>2? 'item-left-box fz54':'item-left-box fz54'">
                                {{ item.cabinetBoxLabel }}
                            </view>
                            <view>号格口</view>
                        </view>
                        <view class="item-right">
                            <view class="item-checkCode">
                                <view class="flex-start">
                                    <view v-if="item.orderType===2" class="label">储物</view>
                                    <view v-if="item.secretWaybill === 1" class="label">隐私</view>
                                    <view class="checkCode">{{ item.checkCode }}</view>
                                </view>
                                <view class="item-btn" @click="isPick(item)">取出</view>
                            </view>
                            <view v-if="item.secretWaybill === 1">
                                手机尾号: {{ item.receiverMobileLast4 }}
                            </view>
                            <view class="item-price">
                                超时费：
                                <view v-if="item.price > 0">
                                    <span class="fee-un-pay">{{ (item.price / 1000).toFixed(2) }}</span>元
                                </view>
                                <view v-else>暂无</view>
                                <view v-if="item.cabinetType===3" class="label ml20">货架</view>
                            </view>
                            <view class="item-detail">
                                所属点位：
                                <view>
                                    {{ item.cabinetLocationName }}
                                </view>
                            </view>
                            <view class="item-detail">
                                <view v-if="item.orderType!==2" class="mr10">
                                    {{ item.brandName }}:**{{ item.waybillNo.slice(-4) }}
                                </view>
                                <view>
                                    {{ item.inboundTime }}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="paddingBottom100"></view>
            </view>
            <view v-else class="no-result"></view>
        </scroll-view>

        <!-- #ifdef MP-WEIXIN -->
        <view class="advertisement" v-if="!isShow">
            <ad v-if="BrandName === '熊猫智能柜'" unit-id="adunit-134f80e8301336a5"></ad>
            <ad v-if="BrandName === '便利智能柜'" unit-id="adunit-33f31091de8070c4"></ad>
        </view>
        <!-- #endif -->
        <show-modal>
            <template slot="popup">
                <view v-if="show" class="showModalContent">
                    <view>
                        该件为
                        <text class="primary-color fw600">隐私件</text>
                        ，由于无法判断是否为您的包裹，需要通过
                        <text class="primary-color fw600">取件码进行取件</text>
                        ，如果非您的包裹，可以忽略。如果未收到取件短信，可以通过如下方式查看:
                    </view>
                    <view>
                        1.淘宝件可通过支付宝菜鸟小程序查看所有取件码
                    </view>
                    <view>
                        2.拼多多、抖音、京东件可通过商品详情页物流信息查看
                    </view>
                    <view>
                        3.关注公众号，回复快递单号后6位进行查询
                    </view>
                </view>
            </template>
        </show-modal>
    </view>
</view>
