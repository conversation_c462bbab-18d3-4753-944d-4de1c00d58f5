.navber{
  width: 100vw;
  height: 100upx;
  font-size: 30upx;
  color:#FFFFFF;
  text-align: center;
  align-items: center;
  line-height: 100upx;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  .navber-title{
    display: inline-block;
    flex-grow: 0.9;
  }
  .navber-left{
    display: inline-block;
  }
}

.container{
  width: 750upx;
  position: fixed;

  .container-code{
    background-color: $config-color;
    padding-bottom: 20upx !important;

    .title{
      color: #FFFFFF;
      font-weight: bold;
      font-size: 32upx;
      padding: 20upx 0;
    }

    .input{
      display: flex;
      align-items: center;
      //border: 2px solid #ccc;
      background-color: #FFFFFF;
      border-radius: 10upx;
      padding: 0 20upx;
      .input-style{
        width: 100% !important;
        font-size: 54upx !important;
        color: $config-color;
      }
    }
  }
  .padding030 {
    padding: 0 30upx;
  }

  .scroll-view{
    position: absolute;
    top: 200upx;
    left: 0;
    right: 0;
    bottom: 0;
    padding-top: 10upx;
    padding-bottom: 430upx;

    .scroll-view-list {
      overflow: auto;
      margin: auto;
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      .paddingBottom100{
        padding-bottom: 100upx;
      }
    }

    .item{
      width: 690upx;
      margin-left: 30upx;
      background: #FFFFFF;
      margin-bottom: 20upx;
      border-radius: 10upx;
      padding:  24upx 30upx;
      box-sizing: border-box;
      display: flex;

      .item-left{
        width: 150upx;
        height: 150upx;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        padding: 20upx;
        border-radius: 10upx;
        background-color: #F5F5F5;

        .item-left-box{
          color: $config-color;
          font-weight: 600;
        }

        .fz80{
          font-size: 80upx;
        }

        .fz54{
          font-size: 54upx;
        }
      }

      .item-right{
        margin-left: 20upx;
        width: 80%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .label{
          background-color: $config-color;
          padding: 0 10upx;
          border-radius: 10upx;
          color: #FFFFFF;
          margin-right: 10upx;
        }

        .item-checkCode{
          display: flex;
          align-items: center;
          justify-content: space-between;

          .checkCode{
            font-size: 40upx;
            font-weight: 600;
          }

          .item-btn{
            background-color: $config-color;
            padding: 10upx 40upx;
            border-radius: 10upx;
            color: #FFFFFF;
          }
        }

        .item-price{
          display: flex;
          align-items: center;
        }

        .item-detail{
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .space-two {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .box-label{
    font-size: 32upx;
    font-weight: bold;
    color: $config-color;
  }

  .fee-un-pay{
    font-size: 40upx;
    font-weight: bold;
    color: $config-color;
  }

  .showModalContent{
    color: #1b1b1b;
    text-align: left;
    font-size: 28upx;
  }

}
