<view class="container2">
  <u-swiper
    :height="180"
    class="bgTop"
    :list="bannerList"
    circular
    @click="arouse"
    indicatorMode="dot"
    indicator
  >
  </u-swiper>
  <view class="middle">
    <view
      v-for="(item, index) in middleMenuList"
      :key="index"
      class="middle-item"
      @click="toNext(item)"
    >
      <view class="item-text">{{ item.name }}</view>
      <img :src="item.url" class="item-img" alt="" />
    </view>
  </view>
  <u-popup
    :show="isShowAuthMobile"
    mode="bottom"
    round="30"
    :closeable="true"
    @close="this.isShowAuthMobile = false"
  >
    <view class="popup-content">
      <view class="detail-title">授权手机号</view>
      <view class="content">
        <view class="message">
          <text class="label">申请获取您的手机号</text>
          <text class="normal"
            >为了更好的体验{{ BrandName }}小程序，{{ BrandName
            }}申请获取您的手机号，您的信息不会用于商业途径。</text
          >
        </view>
        <view class="btn-view">
          <!-- 微信小程序环境 -->
          <button
            class="btn auth"
             hover-class="btn-hover"
            open-type="getPhoneNumber"
            @getphonenumber="throttle(getPhoneNumber, $event, 1500)"
            v-if="!isAlipay()"
          >
            立即授权
          </button>
          <!-- 支付宝小程序环境 -->
          <button
          class="btn auth"
          hover-class="btn-hover"
            open-type="getAuthorize"
            @getAuthorize="throttle(getPhoneNumber, $event, 1500)"
            @error="onAuthError"
            scope="phoneNumber"
            v-if="isAlipay()"
          >
            立即授权支付宝
          </button>
        </view>
      </view>
    </view>
  </u-popup>
</view>
