
.container2 {
  width: 750upx;
  position: relative;
}

.top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 690upx;
  position: absolute;
  top: 235px;
  margin-left:30upx;

  .top-item {
    box-sizing: border-box;
    border-radius: 20upx;
    background: #ffffff;
    width: 334upx;
    height: 268upx;
    padding: 20upx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-end;

    .item-text {
      font-size: 36upx;
      width: 100%;
      height: 37upx;
      font-weight: bolder;
      color: #000000;
    }

    .item-img {
      width: 200upx;
      height: 160upx;
      float: right;
    }
  }
}
.middle {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 690upx;
  margin-left:30upx;
  margin-top: 30upx;

  .middle-item {
    box-sizing: border-box;
    border-radius: 20upx;
    background: #ffffff;
    width: 334upx;
    height: 200upx;
    padding: 20upx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 30upx;

    .item-text {
      font-size: 36upx;
      width: 100%;
      height: 37upx;
      font-weight: bolder;
      color: #000000;
    }

    .item-img {
      width: 140upx;
      height: 100upx;
      float: right;
    }
  }
}
.auth-view {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 750upx;
  min-height: 450upx;
  background-color: #ffffff;
  box-shadow: -10upx -10upx 5upx #888888;;
  display: flex;
  justify-content: center;
  align-items: center;
}
.popup-content{
  background-color: #ffffff;
  padding: 30upx;
  position: relative;
  min-height: 500upx;

  .detail-title{
    font-size: 36upx;
    font-weight: 600;
    margin-bottom: 30upx;
  }

  .content{
    .message{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-top: 100upx;
      .label{
        font-size: $uni-font-size-max;
      }
      .normal{
        font-size: $uni-font-size-min;
        color: #c5c5c5;
        margin-top: 10upx;
        text-align: center;
      }
    }
  }
}
.btn-view{
  margin-top: 100upx;
  .btn{
    width: 670upx;
    height: 90upx;
    line-height: 90upx;
    border-radius: 5upx;
    text-align: center;
    font-size: $uni-font-size-sm;
  }
  .auth{
    color: #ffffff;
    background-color: $uni-text-color-global;
    margin-bottom: 40upx;
  }
  .toLogin{
    background-color: #ffffff;
    border: 1px solid $uni-text-color-global;
    color: $uni-text-color-global;
    margin-bottom: 40upx;
  }
}