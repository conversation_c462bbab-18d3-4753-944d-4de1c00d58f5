<script lang="ts">
import 'reflect-metadata';
import {Component, Mixins} from 'vue-property-decorator';
import {outerDto, wechatPhoneDto} from "@/utils/types";
import {bindPhone, checkExpireTime, checkUser, getLoginCode, loginByWechat, removeOuter} from "@/utils/core/uni";
import {Cache, Modal} from "@/utils";
import {CommonMixins} from "@/mixins/commonMixin";
import {ImageMixins} from "@/mixins/ImageMixins";
import {UserMixins} from "@/mixins/userMixin";
import {bindGzh,getCodeDate} from "@/model/login";
import {bannerList, getCabinetInfo, getScanCodeHostCode, openDoor} from "@/model/cabinet";
import config from "@/utils/config";
import modal from "@/utils/core/modal";
import banner1 from '@/static/img/home/<USER>/banner1.png'
import banner2 from '@/static/img/home/<USER>/banner2.png'
import banner3 from '@/static/imgConfig/banner3.png'
import banner4 from '@/static/imgConfig/banner4.png'

@Component({
  name: 'homePageIndex'
})
export default class HomePageIndex extends Mixins(CommonMixins, ImageMixins, UserMixins) {
  private BrandName = config.BrandName
  private statusBarHeight: number = 0
  private navBarHeight: number = 0
  private contentOutHeight: number = 0
  private contentHeight: number = 0
  private isShowAuthUser: boolean = false
  private isScence: boolean = false
  private gzhOpenId: string = ''
  private gzhAppId: string = ''
  private bannerList: any = []
  private bannerListInfo: any = []
  private type = ''
  private ScanType = ''
  private topMenuList: Array<any> = [
    {
      id: 1,
      name: '扫码开柜',
      url: this.getImage('qj')
    },
    {
      id: 2,
      name: '柜机寄件',
      url: this.getImage('jj')
    }
  ]
  private middleMenuList: Array<any> = [
    // #ifdef MP-WEIXIN
    {
      id: 1,
      name: '扫码开柜/开门',
      url: this.getImage('qj')
    },
    {
      id: 5,
      name: '我的快递',
      url: this.getImage('wdkd')
    },
    // {
    //   id: 5,
    //   name: '扫码开门',
    //   url: this.getImage('openDoor')
    // },
    {
      id: 2,
      name: '柜机寄件',
      url: this.getImage('jj')
    },
    {
      id: 3,
      name: '储物下单',
      url: this.getImage('zcdd')
    },
// #endif
    {
      id: 4,
      name: '附近服务点',
      url: this.getImage('fjfwd')
    },
    {
      id: 6,
      name: '忘记取件码',
      url: this.getImage('wjqjm')
    },
  ]

  isAlipay() {
      return uni.getSystemInfoSync().app === 'alipay'
    }

  async onShow() {
    await getLoginCode()
    await this.goPackageList()
    if (this.gzhOpenId && this.gzhAppId) {
      await this.binGzhId()
    }
    // 判断是否在微信环境中，如果是则获取广告信息
    // 这里使用isWechat()函数检测当前运行环境是否为微信
    if(!this.isAlipay()){
      await this.getAdvertisement()
    }
  }

  async binGzhId() {
    const info = {
      gzhAppId: this.gzhAppId,
      gzhOpenId: this.gzhOpenId
    }
    await bindGzh(info);
  }

  /**
   * 页面加载时的生命周期函数
   * @param e - 外部传入的参数对象
   */
  async onLoad(e: outerDto) {
    const launchOptions = Cache.getItem('launchOptions')
    const qrCode = launchOptions?.query?.key1
    const cabinetLocationCode = launchOptions?.query?.cabinetLocationCode
    if(!qrCode && cabinetLocationCode && cabinetLocationCode.trim() !== '' && this.isAlipay()){
      if(cabinetLocationCode && cabinetLocationCode.trim() !== ''){
        const info = {
          cabinetLocationCode: cabinetLocationCode
        }
        return  uni.navigateTo({url: '/homePages/receive/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
      }
    }
    // 设备碰撞
    if (qrCode && qrCode.trim() !== '' && this.isAlipay()) {
      if(!checkUser()){
          uni.redirectTo({
            url: '/authPages/index/index'
          })
      }
      try {
        const codeDate = await getCodeDate({code: qrCode})
        if (codeDate) {
          this.isScence = true
          this.ScanType = 'wx' // 设置为微信小程序码类型，保持与微信扫码逻辑一致
            // 使用专门的支付宝小程序码处理方法
            await this.scanAlipayCode(codeDate)
          // await this.scanWxAppletCode(codeString)
        } else {
          // 当 codeDate 为空但请求成功的情况
          Modal.toast('碰一碰数据为空，请重新支付宝碰一碰')
        }
      } catch (error) {
        console.error('获取碰一碰数据失败:', error)
        // 使用 Modal.showErrorToast 显示带有错误图标的提示
        Modal.showErrorToast('获取二维码数据失败，请稍后重试')
      }
    }
    // 判断当前环境是否为支付宝
    if (this.isAlipay()) {
      console.log('支付宝进入');
      // 在支付宝环境中显示分享菜单
      // uni.showShareMenu({
      //   success: function (res) {
      //     console.log('显示分享菜单',res);
      //   }
      // })
    } else {
      console.log('微信进入');
      // 在微信环境中显示分享菜单，并配置分享选项
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      })
    }
    
    // 隐藏返回首页按钮
    uni.hideHomeButton()
    // 获取系统信息（状态栏高度等）
    this.getSystemInfo()
    // 检查是否有外部传入参数
    if (Object.keys(e).length > 0 && !this.isAlipay()) {
      console.log('外部传入参数', JSON.stringify(e))
      // 处理微信小程序码场景值
      if (e.scene) {
        this.isScence = true
        this.ScanType = 'wx'
        await this.scanWxAppletCode(e.scene)
      }
      // 处理柜机位置编码
      if (e.cabinetLocationCode !== undefined) {
        (e.cabinetLocationCode.trim() !== '') && Cache.setItem('cabinetLocationCode', e.cabinetLocationCode.trim())
      }
      // 处理订单类型
      if (e.orderType !== undefined) {
        Cache.setItem('orderType', e.orderType)
      }
      // 处理过期时间
      if (e.expireTime !== undefined) {
        Cache.setItem('expireTime', e.expireTime)
      }
      // 处理公众号AppID
      if (e.gzhAppId !== undefined) {
        this.gzhAppId = e.gzhAppId
        Cache.setItem('gzhAppId', e.gzhAppId)
      }
      // 处理公众号OpenID
      if (e.gzhOpenId !== undefined) {
        this.gzhOpenId = e.gzhOpenId
        Cache.setItem('gzhOpenId', e.gzhOpenId)
      }
      // 处理主机索引
      if (e.i !== undefined && e.i !== 'null') {
        Cache.setItem('hostIndex', e.i)
      } else {
        Cache.removeItem('hostIndex')
      }
      // 处理URL参数q（通常包含扫描的二维码信息）
      if (e.q !== undefined) {
        let result = decodeURIComponent(e.q)
        let isDoor = false
        // 判断是否为开门二维码
        if (result.startsWith('http://qr.711bear.com/xm') || result.startsWith('http://qr.711bear.com/bl')) {
          isDoor = true
        }
        // 处理微信公众号二维码扫描结果
        await this.scanWxOfficialAccountCode(result, isDoor)
      }
    }
    // 获取轮播图列表
    await this.getBannerList()
  }

  // 获取小程序广告列表
  async getBannerList() {
    let data = {
      adTerminal: 5
    }
    let res = await bannerList(data)
    if (res.length > 0) {
      this.bannerListInfo = res
      res.map((item: any) => {
        this.bannerList.push(item.fileUrl)
      })
    } else {
      if (process.env.VUE_APP_CHANNEL === 'yzgp') {
        this.bannerList = [banner3, banner4]
      } else {
        this.bannerList = [banner1, banner2]
      }
    }
  }

  // 唤起广告小程序
  arouse(e: any) {
    let link = this.bannerListInfo[e].link
    if (link.indexOf('wxuniapp') > -1) {
      let params: any[] = decodeURIComponent(link.replace('wxuniapp:', '')).split(',')
      let appId = params[0]
      let path = params[1]
      appId = appId.slice(appId.indexOf('=') + 1)
      path = path.slice(path.indexOf('=') + 1)
      path = path.replace(new RegExp('&amp;', 'g'), "&");
      uni.navigateToMiniProgram({
        appId: appId, // 跳转目标小程序的id
        path: path, // 目标小程序的页面路径
        extraData: {
          // 需要携带的参数
        },
        success(res) {
          // 打开成功
        }
      })
    }
  }

  // 获取广告
  async getAdvertisement() {
    uni.request({
      url: 'https://kl.haohuisheng555.cn/?channel=w26-12&callback=myfunc',
      data: '',
      method: 'GET',
      header: {},
      async success(res: any) {
        uni.setClipboardData({
          showToast: false,
          data: res.data.text,
          success() {
            uni.hideLoading()
          }
        });
      }
    })
  }

  async scanWxOfficialAccountCode(result: string, isDoor = false) {
    if (!result) return
    if(result == '*'){ return Modal.toast('二维码无效检查开的小程序是否正确！') }
    if (isDoor) {
      if (result.startsWith('http://qr.711bear.com/xm') || result.startsWith('http://qr.711bear.com/bl')) {
        let index = result.indexOf('qrSeq=')
        let indexcode = result.indexOf('qrCode=')
        let qrSeqNo
        if (index !== -1) {
          // 如果存在qrSeq参数，获取qrSeq的值
          qrSeqNo = result.substring(index + 6)
        } else if (indexcode !== -1) {
          // 如果不存在qrSeq但存在qrCode参数，获取qrCode的值
          qrSeqNo = result.substring(indexcode + 7)
        } else {
          // 如果两个参数都不存在，直接返回
          Modal.showErrorToast('二维码无效未获取到参数值！')
          return
        }
        const info = {
          qrSeqNo: qrSeqNo,
        }
        const code = await openDoor(info)
        console.log('开门', code) 
        
        if (code) {
          const info = {
            cabinetLocationCode:code
          }
          uni.navigateTo({url: '/homePages/receive/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
          uni.showToast({
            title: '开门成功',
            icon: 'none'
          })
        }

      }
    } else {
      if (result.startsWith('http://weixin.qq.com/')) {
        const res = await getScanCodeHostCode({data: result})
        if (res) {
          Cache.setItem('hostIndex', res.hostIndex)
          const info = {
            cabinetLocationCode: res.code
          }
          uni.navigateTo({url: '/homePages/receive/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
        }
      } else if (result.startsWith('http://s.711bear.com/sc') || result.startsWith('http://blzng-s.kuaidiyz.com/sc')) {
        let index = result.indexOf('c=')
        let code = result.substring(index + 2)
        const info = {
          cabinetLocationId: code
        }
        uni.navigateTo({url: '/homePages/receive/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
      }
    }
  }

  /**
   * 微信小程序码处理
   * @param path
   */
  async scanWxAppletCode(path: string) {
    await this.getUserInfo()
    if (!path) {
      return
    }
    if (path.indexOf('scene') > 0) {
      this.isScence = true
    }
    let cid = ''
    let code = ''
    let type = ''
    let hostIndex = ''
    let params: any[] = decodeURIComponent(path.replace('pages/home/<USER>/index?scene=', '')).split(',')
    console.log('params', params)
    params.forEach((item) => {
      if (item.startsWith('c')) {
        cid = item.split('c-')[1]
        Cache.setItem('cabinetLocationId', cid)
      }
      if (item.startsWith('t')) {
        type = item.split('t-')[1]
        Cache.setItem('scanType', type)
      }
      if (item.startsWith('l')) {
        code = item.split('l-')[1]
        Cache.setItem('cabinetLocationCode', code)
      }
      if (item.startsWith('i')) {
        hostIndex = item.split('i-')[1]
        Cache.setItem('hostIndex', hostIndex)
      } else {
        Cache.removeItem('hostIndex')
      }
    })
    if (Cache.getItem('user') != undefined) {
      await this.goPackageList()
    } else {
      uni.redirectTo({
        url: '/authPages/index/index'
      })
    }
  }

    /**
   * 支付宝小程序码处理
   * @param codeData 支付宝碰一碰返回的数据对象
   */
   async scanAlipayCode(codeData: any) {
    await this.getUserInfo()
    if (!codeData) {
      return
    }
    
    this.isScence = true
    
    // 从codeData中提取参数并缓存
    if (codeData.c) {
      Cache.setItem('cabinetLocationId', codeData.c)
    }
    if (codeData.t) {
      Cache.setItem('scanType', codeData.t)
    }
    if (codeData.l) {
      Cache.setItem('cabinetLocationCode', codeData.l)
    }
    if (codeData.i) {
      Cache.setItem('hostIndex', codeData.i)
    } else {
      Cache.removeItem('hostIndex')
    }
    
    if (Cache.getItem('user') != undefined) {
      await this.goPackageList()
    } else {
      uni.redirectTo({
        url: '/authPages/index/index'
      })
    }
  }



  async goPackageList() {
    if (Cache.getItem('user') != undefined) {
      await this.getUserInfo()
    }
    let isOverTime: boolean = checkExpireTime()
    if (isOverTime) {
      await Modal.alert('二维码失效,请重新扫描')
      removeOuter()
      return
    }
    let cabinetLocationCode = Cache.getItem('cabinetLocationCode') || ''
    let scanType = Cache.getItem('scanType') || ''
    let cabinetLocationId = Cache.getItem('cabinetLocationId') || ''
    if (this.isAuthMobile) {
      let isOverTime: boolean = checkExpireTime()
      if (isOverTime) {
        await Modal.alert('二维码失效,请重新扫描')
        removeOuter()
        return
      }
      const info = {
        cabinetLocationCode: cabinetLocationCode.toString().trim(),
        cabinetLocationId: cabinetLocationId.toString().trim()
      }
      if (cabinetLocationCode || cabinetLocationId) {
        removeOuter()
        // 判断是否是储物下单
        if (this.type === 'cw') {
          if (scanType != 6) {
            await modal.alert('请扫描正确的储物下单二维码', '')
            return
          }
        }
        if (this.ScanType === 'wx') {
          this.type = 'cw'
          this.ScanType = ''
        }
        if (this.isScence) {
          if (scanType == 3) {
            uni.navigateTo({url: '/homePages/receive/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
          } else if (scanType == 2) {
            uni.navigateTo({url: '/homePages/send/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
          } else if (scanType == 6 && this.type === 'cw') {
            let res = await getCabinetInfo({code: cabinetLocationCode})
            if (res.switchKeep === 0) {
              await modal.alert('该柜机暂不支持储物', '')
            } else {
              uni.navigateTo({url: '/homePages/storageOrder/storageOrderCreat/storageOrderCreat?info=' + encodeURIComponent(JSON.stringify(info))})
            }
          }
              // if (type === '2' && code) {
              //   let goSendListType = await getSendList({cabinetLocationCode: code, orderSendStatus: 1, current: 1, size: 1})
              //   if (goSendListType.total === 0) {
              //     let res = await getCabinetInfo({code})
              //     let data = {
              //       name: res.name,
              //       code: res.code,
              //       sendJson: res.sendJson
              //     }
              //     let cabinetInfo = JSON.stringify(data)
              //     uni.navigateTo({
              //       url: `/homePages/send/sendForm/sendForm?cabinetInfo=${cabinetInfo}`
              //     })
              //   } else if (goSendListType.total === 1) {
              //     let data = {
              //       orderId: goSendListType.records[0].orderId
              //     }
              //     let orderId = JSON.stringify(data)
              //     uni.navigateTo({
              //       url: `/homePages/send/orderDetail/orderDetail?orderId=${orderId}`
              //     })
              //   } else if (goSendListType.total > 1) {
              //     let res = await getCabinetInfo({code})
              //     let data = {
              //       cabinetLocationCode: code,
              //       cabinetLocationId: res.cabinetLocationId
              //     }
              //     let info = JSON.stringify(data)
              //     uni.navigateTo({
              //       url: `/homePages/send/packageList/packageList?info=${info}`
              //     })
              //   }
          // }

          else {
            await Modal.alert('请扫描正确的柜机取件二维码')
            this.isScence = false
          }
        } else {
          uni.navigateTo({url: '/homePages/receive/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
        }
        this.type = ''
      }
    }
  }

  async getPhoneNumber(data: wechatPhoneDto) {
    try {
      if (data.detail.errMsg === 'getPhoneNumber:ok'|| this.isAlipay()) {
        console.log('进入支付宝' ,data)
      const res: any = await bindPhone(data)
      if (res != null) {
        this.isShowAuthMobile = false
        await this.getUserInfo(true)
        Modal.toast('手机号授权成功')
        await this.goPackageList()
      }
    }
    }catch (error) {
        console.error('手机号授权失败:', error)
        Modal.toast('手机号授权失败，请重试')
    }
   
  }

  scan(type: string) {
    this.type = type
    uni.scanCode({
          success: res => {
            if (res.path && res.path.startsWith('pages/home/<USER>/index?q=')&& res.result) {
             return Modal.alert('请打开正确的小程序扫码二维码！')
            }
            if (res.path) {
              // 扫码静态码
              console.log('扫码静态码', res)
              this.scanWxAppletCode(res.path)
            } else if (res.result) {
               // 扫码公众号静态码
              console.log('扫码公众号静态码', res.result.startsWith('http://weixin.qq.com/'))
              res.result.startsWith('http://weixin.qq.com/') ?  this.scanWxOfficialAccountCode(res.result,false) : this.scanWxOfficialAccountCode(res.result,true)
            } else {
              Modal.alert('请扫描正确的柜机二维码')
            }
          },
          fail: err => {
            Modal.alert('扫码失败')
          }
        }
    )
  }

  // 扫码开柜目前已经优化掉了
  scanOpenDoor() {
    uni.scanCode({
          success: res => {
            if (res.result) {
              // 扫码公众号静态码
              console.log('扫码公众号静态码', res)
              this.scanWxOfficialAccountCode(res.result, true)
            } else {
              Modal.alert('请扫描正确的开门二维码')
            }
          },
          fail: err => {
            // Modal.alert('扫码失败')
          }
        }
    )
  }

  async toNext(item: any) {
    if (item.name !== '附近服务点') {
      await this.getUserInfo()
    }
    switch (item.name) {
      case '扫码开柜/开门':
        if (!this.isAuthMobile) {
          this.isShowAuthMobile = true
          return false
        }
        this.scan('qj')
        break;
      case '扫码开门':
        if (!this.isAuthMobile) {
          this.isShowAuthMobile = true
          return false
        }
        this.scanOpenDoor()
        break;
      case '附近服务点':
        uni.navigateTo({url: '/homePages/nearCabinet/nearCabinet'})
        break;
      case '柜机寄件':
        uni.navigateTo({url: '/homePages/send/cabinetLocationList/cabinetLocationList'})
        break;
      case '储物下单':
        if (!this.isAuthMobile) {
          this.isShowAuthMobile = true
          return false
        }
        this.scan('cw')
        break;
      case '我的快递':
        uni.reLaunch({url: '/pages/search/index/index'})
        break;
      case '忘记取件码':
        uni.navigateTo({url: '/homePages/forgetCode/forgetCode'})
        break;
      default:
        Modal.toast('敬请期待')
    }
  }


  async login() {
    const res: any = await loginByWechat()
    if (res != null && res.isLogin) {
      Modal.toast('授权用户信息成功')
      this.isShowAuthUser = false
      let isAuthMobile: boolean = checkUser()
      if (!isAuthMobile) {
        this.isShowAuthMobile = true
      }
    }
  }

  upxTopx(value: number) {
    return uni.upx2px(value);
  }

  getSystemInfo() {
    const info = uni.getSystemInfoSync()
    // 状态栏高度
    this.statusBarHeight = info.statusBarHeight ?? 24
    // 导航栏高度
    this.navBarHeight = info.system.indexOf('iOS') > -1 ? 44 : 46
    // 内容区域高度
    this.contentOutHeight = info.windowHeight
    this.contentHeight = info.windowHeight - this.statusBarHeight - this.navBarHeight - this.upxTopx(100)
  }
}
</script>

<template src="./index.html"></template>
<style lang="scss" scoped src="./index.scss"></style>
