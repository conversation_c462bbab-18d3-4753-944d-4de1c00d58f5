<template>
  <view class="container">
    <web-view :src="url"></web-view>
  </view>
</template>

<script lang="ts">
import {Component, Vue, Watch} from "vue-property-decorator"
import {<PERSON><PERSON>, Modal} from "@/utils/index"
import {LogOut} from "@/utils/core/uni"

@Component({
  name: 'webView'
})
export default class webView extends Vue {
  private url: string = ''

  onLoad(options: any) {
    this.url = options.url
  }
}
</script>

<style lang="scss" scoped>
.container {
  position: relative;
}
</style>
