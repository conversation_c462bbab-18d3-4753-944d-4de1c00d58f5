<view class="container">
  <view class="top" :style=" isAlipay() ? 'height: 2.2rem':''">
    <view class="user-view"    :style=" isAlipay() ? 'margin-top: 0.5rem':''" >
      <view class="left">
        <view class="user-logo" @click="toUserInfo">
          <img :src="getUserAvatar()" alt="" />
        </view>
        <view class="user-name">
          <view class="flex-row" @click="toUserInfo">
            <view>{{ getUserNickName() }} </view>
          </view>
          <!--                    <button v-if="isShowUserMobile1" @click="checkUser"-->
          <!--                            class="mobile-button">-->
          <!--                        绑定手机号1-->
          <!--                    </button>-->
          <view>
            <view v-if="showMobile1"
              >{{ user.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}</view
            >
            <button
              v-else-if="isShowAuthBtn"
              open-type="getPhoneNumber"
              @getphonenumber="goPhoneBind"
              class="mobile-button"
            >
              <text>绑定手机号</text>
            </button>
            <!-- <button class="mobile-button" open-type="getAuthorize" @getAuthorize="throttle(goPhoneBind, $event, 1500)" 
          @error="onAuthError" 
          scope='phoneNumber'
          v-if="isAlipay()">
          绑定手机号
        </button> -->
          </view>
        </view>
      </view>
      <view class="right" @click.capture="toUserInfo">
        <img
          src="../../../static/img/right-arrow.png"
          alt=""
          style="width: 20upx; height: 30upx"
        />
      </view>
    </view>
  </view>

  <view class="operations">
    <view class="title">个人设置</view>
    <view class="menus">
      <view
        class="item"
        @click="toNext(item)"
        v-for="(item, index) in operations"
        :key="index"
      >
        <image :src="item.icon"></image>
        <view class="label">{{ item.label }}</view>
      </view>
    </view>
  </view>
</view>
