.container {
  position: relative;

  .top{
    width: 750upx;
    height: 320upx;
    padding: 0 30upx;
    box-sizing: border-box;
    background: url("@/static/img/home/<USER>/bg.png");
    background-size: 750upx 320upx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    .navBar {
      color: #000000;
      font-size: 35upx;
      font-weight: 600;
    }
    .user-view {
      margin-top: 170upx;
      width: 690rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      .left{
        display: flex;
        flex-direction: row;
        flex:1;
      }
      .right{
        display: flex;
        justify-content: center;
        width: 100upx;
        align-items: center;
      }
      .user-logo {
        width: 114upx;
        height: 114upx;
        border-radius: 70upx;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        image{
          width: 100%;
          height: 100%;
        }
      }

      .user-name {
        height: 130upx;
        margin-left: 30upx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        font-size: 30upx;
        font-weight: 400;
        color: #333333;
        .real-name {
          margin-left: 16upx;
          border-radius: 20upx;
          padding: 2upx 10upx;
          height: 30upx;
          border: 1px solid $config-color;
          display: flex;
          justify-content: center;
          align-items: center;
          .text{
            font-size: 22upx;
            color: $config-color;
          }
        }
        .mobile-button{
          color: #000000;
          padding: 0;
          line-height: 1.2;
          background-color: transparent;
          font-size: 28upx;
          border: none;
          //display: contents;
        }
        .mobile-button:after{
          border: none;
          color: #000000;
        }
        .desc{
          font-size: $uni-font-size-sm;
          color: #404040;
          font-weight: normal;
        }
      }
    }
    .top-contexnt{
      width: 100%;
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: flex-end;
      padding-bottom: 30upx;
      .border{
        width: 1px;
        height: 70upx;
        background-color: #eeeeee;
        margin-bottom: 20upx;
      }
      .item{
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .number{
          font-size: 45upx;
          font-weight: 600;
          color: $uni-text-color-default;
        }
        image{
          width: 40upx;
          height: 40upx;
          margin-bottom: 20upx;
        }
        .label{
          font-size: $uni-font-size-sm;
          color: $uni-text-color-grey;
        }
      }
    }
  }

  .operations{
    margin-top: 20upx;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 30upx;
    width: 750upx;
    box-sizing: border-box;
    .title{
      font-size: 30upx;
      font-weight: 400;
      color: #000000;
    }
    .menus{
     display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    flex-direction: row;
    margin-top: 30rpx;
    flex-wrap: wrap;
    justify-content: space-between;

    }
    .item{
    background-color: #ffffff;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 0 30rpx;
    /* width: 33% ; */
    margin-bottom: 30rpx;
      image{
        width: 50upx;
        height: 50upx;
      }
      .label{
        flex: 1;
        margin-top: 10upx;
        text-align: left;
        font-size: 22upx;
        color: black;
      }
    }
  }
}
