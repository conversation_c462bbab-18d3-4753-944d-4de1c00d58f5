<script lang="ts">
import {Component} from "vue-property-decorator"
import {<PERSON><PERSON>, Modal} from "@/utils"
import {bindPhone, getLoginCode, loginByWechat, LogOut} from "@/utils/core/uni"
import {wechatPhoneDto} from "@/utils/types";
import {ImageMixins} from "@/mixins/ImageMixins";
import {getUserInfo} from '@/model/login';

export interface operationDto {
  label: string;
  type: string;
  icon: string;
}

@Component({
  name: 'user'
})
export default class User extends ImageMixins {
  private statusBarHeight: number = 0
  private navBarHeight: number = 0
  private user: any = {}
  private showMobile1: boolean = false
  private isShowAuthBtn: boolean = false

  private operations: operationDto[] = [
    {
      label: '保管设置',
      type: 'keep',
      icon: this.getImage('keep')
    },
    {
      label: '手机号管理',
      type: 'friends',
      icon: this.getImage('friends')
    },
    {
      label: '申请布点',
      type: 'apply',
      icon: this.getImage('apply')
    },
    {
      label: '地址管理',
      type: 'address',
      icon: this.getImage('address')
    },
    {
      label: '更多期待',
      type: 'more',
      icon: this.getImage('more')
    }
  ]

  // 获取设备信息
  getSystemInfo() {
    const info = uni.getSystemInfoSync()
    // 状态栏高度
    this.statusBarHeight = info.statusBarHeight ?? 24
    // 导航栏高度
    this.navBarHeight = 44
  }

  clearCache() {
    LogOut()
  }
  isAlipay() {
        return uni.getSystemInfoSync().app === 'alipay'
  }

  getUserAvatar() {
    if (this.user != undefined) {
      if (this.user.avatar) {
        return this.user.avatar
      }
    }
    return this.getImage('logo')
  }

  getUserNickName() {
    if (this.user != undefined) {
      if (this.user.nickname) {
        return this.user.nickname
      }
    }
    return '点击登录账户'
  }

  async getUserLogo() {
    const res: any = await loginByWechat();
    if (res != null && res.isLogin) {
      await this.getUserInfo()
      Modal.toast('授权用户登录成功')
    }
  }

  async getUserInfo() {
    const user: any = await getUserInfo();
    this.user = user
    this.isShowUserMobile1()
    this.showAuthBtn()
    Cache.setItem('user', user)
  }

  onLoad(options :any) {
    console.log(options)
  }

  isShowUserMobile1() {
    this.showMobile1 = this.user !== undefined && this.user.phone != undefined
  }

  showAuthBtn() {
    if (this.user !== undefined) {
      this.isShowAuthBtn = this.user.phone == '' || this.user.phone == null
    }
  }

  async onShow() {
    await getLoginCode()
    this.getSystemInfo()
    const user = await Cache.getItem('user')
    this.user = user
    if (user !== undefined) {
      this.user = Object.assign({}, user)
    }
    this.isShowUserMobile1()
    this.showAuthBtn()
  }

  async checkUser() {

    if (this.user != undefined) {

    } else {
      await Modal.confirm('登录失效，请重新登录', '温馨提示', '暂不', '立即登录')
      uni.redirectTo({
        url: '/authPages/index/index'
      })
    }
  }

  async goPhoneBind(data: wechatPhoneDto) {
    try {
      if (data.detail.errMsg === 'getPhoneNumber:ok') {
      const res: any = await bindPhone(data)
      if (res != null) {
        await this.getUserInfo()
        Modal.toast('手机号授权成功')
      }
    }
    }catch (error) {
        console.error('手机号授权失败:', error)
        Modal.toast('手机号授权失败，请重试')
    }
  }

  async toUserInfo() {
    if (this.user != undefined) {
      uni.navigateTo({url: '/myPages/userInfo/userInfo'})
    } else {
      uni.navigateTo({
        url: '/authPages/index/index'
      })
    }
  }

  async toNext(item: operationDto) {
    let url: string = ''
    switch (item.type) {
      case 'keep':
        url = '/myPages/keep/keep'
        break
      case 'friends':
        url = '/myPages/friends/friends'
        break
      case 'apply':
        url = '/myPages/apply/apply'
        break
      case 'address':
        url = '/homePages/send/addressList/addressList?type=address'
        break
      case 'more':
        url = ''
        break
      default:
        url = ''
        break
    }
    if (url == '') {
      Modal.toast('敬请期待')
    } else {
      if (this.user != undefined) {
        uni.navigateTo({url: url})
      } else {
        await Modal.confirm('登录失效，请重新登录', '温馨提示', '暂不', '立即登录')
        uni.redirectTo({
          url: '/authPages/index/index'
        })
      }
    }
  }
}
</script>

<template src="./index.html"></template>
<style lang="scss" scoped src="./index.scss"></style>
