<view class="container" :catchtouchmove="true">
    <view class="top">
        <view class="bar">
            <search-input v-model="keyword" placeholder="请输入取件码" @inputConfirm="getList"
                          :show-scan="false"></search-input>
            <view class="num-view">
                <view class="item" @click="changeTab('qj')">
                    <text class="label" :class="{'active':type === 'qj'}">我的取件</text>
                    <view class="number-bar" v-if="total > 0 && type ==='qj'">
                        <text class="number">{{ total }}</text>
                    </view>
                </view>
                <view class="item" @click="changeTab('jj')">
                    <text class="label" :class="{'active':type === 'jj'}">我的寄件</text>
                    <view class="number-bar" v-if="total >0 && type ==='jj'">
                        <text class="number">{{ total }}</text>
                    </view>
                </view>
                <view class="item" @click="changeTab('zc')">
                    <text class="label" :class="{'active':type === 'zc'}">储物订单</text>
                    <view class="number-bar" v-if="total >0 && type ==='zc'">
                        <text class="number">{{ total }}</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="filter" v-if="type !=='jj'">
            <view class="filer-item" :class="{'active': packageType === 'UNPICK'}" @click="changePackageType('UNPICK')">
                未取包裹
            </view>
            <view class="filer-item" :class="{'active': packageType === 'ALL'}" @click="changePackageType('ALL')">
                全部包裹
            </view>
        </view>
    </view>

    <view class="bottom2" v-if="type ==='jj'">
        <view class="bottom-btn" @click="scan">
            <view class="text">
                {{ getTitle(type) }}
            </view>
        </view>
    </view>
    <scroll-view scroll-y
                 :class="{'scroll-view': type ==='qj', 'scroll-view2': type === 'jj','scroll-view': type === 'zc'}"
                 @scrolltolower="loadMore" :catchtouchmove="true">
        <view v-if="list.length>0">
            <view v-for="(item, index) in list" :key="index">
                <view class="scroll-item" v-if="type ==='qj'">
                    <view class="space-two">
                        <view class="cabinet-name">{{ getItemTitle(item) }}</view>
                        <view class="mini-label" v-if="item.hasOutbound === 0"> 取件码<span
                                class="check-code">{{ item.checkCode.replace(/(\d{4})(?=\d)/g, '$1 ') }}</span>
                        </view>
                        <view class="label" v-else-if="item.hasOutbound === 1"> 已取</view>
                    </view>
                    <view style="padding: 6upx"></view>
                    <view class="space-two">
                        <view class="label">快递单号</view>
                        <view class="label">{{ item.waybillNo }}</view>
                    </view>
                    <view class="space-two">
                        <view class="label">快递品牌</view>
                        <view class="label">{{ item.brandName }}</view>
                    </view>
                    <view class="space-two">
                        <view class="label">投递时间</view>
                        <view class="label">{{ item.inboundYmd }}</view>
                    </view>
                    <view class="space-two">
                        <view class="label">存放时间</view>
                        <view class="label">{{ item.serviceDurationDesc }}</view>
                    </view>
                    <view class="space-two" v-if="item.storeType === 1">
                        <view class="label">格口类型</view>
                        <view class="label">{{ getBoxTypeName(item.cabinetBoxType) }}</view>
                    </view>
                    <view class="space-two" v-if="item.storeType === 1">
                        <view class="label">格口号</view>
                        <view class="label">{{ item.cabinetBoxLabel }}</view>
                    </view>
                    <view v-if=" item.hasOutbound === 1" class="space-two">
                        <view class="label">取件码</view>
                        <view class="label">{{ item.checkCode }}</view>
                    </view>
                    <view style="padding: 6upx"></view>
                    <view class="space-one-end">
                        <view class="fee-un-pay" v-if="item.hasOutbound === 0 && item.predictPrice > 0">
                            预估费用￥{{ (item.predictPrice / 1000).toFixed(2) }}
                        </view>
                        <view class="fee-al-pay" v-if="item.hasOutbound === 1 && item.outboundUserFee > 0">
                            已收费￥{{ (item.outboundUserFee / 1000).toFixed(2) }}
                        </view>
                    </view>
                </view>
                <view class="scroll-item" v-else-if="type ==='jj'">
                    <view @click="toPackageDetail(item)">
                        <view class="space-two align-center">
                            <view class="flex-row align-center">
                                <view class="cabinet-name">{{ item.brandName }}</view>
                                <view class="label pl15">{{ waybillNo(item.waybillNo) }}</view>
                            </view>
                            <view class="flex-row align-center">
                                <u-icon name="clock" :color="iconColor" size="16px"></u-icon>
                                <text class="primary-color">{{ orderStatus(item.sendStatus) }}</text>
                            </view>
                        </view>
                        <view style="padding: 12upx"></view>
                        <view class="address-item">
                            <img :src="getImage('send')" alt="">
                            <view class="address-info">
                                <view class="name">
                                    {{ getAddressUser(item.senderName, item.senderMobile) }}
                                </view>
                                <view class="info">
                                    {{
                                    getAddress(item.senderProvinceName, item.senderCityName, item.senderAreaName, item.senderStreetName, item.senderAddress)
                                    }}
                                </view>
                            </view>
                        </view>
                        <view class="border"></view>
                        <view class="address-item">
                            <img :src="getImage('receive')" alt="">
                            <view class="address-info">
                                <view class="name">
                                    {{ getAddressUser(item.receiverName, item.receiverMobile) }}
                                </view>
                                <view class="info">
                                    {{
                                    getAddress(item.receiverProvinceName, item.receiverCityName, item.receiverAreaName, item.receiverStreetName, item.receiverAddress)
                                    }}
                                </view>
                            </view>
                        </view>
                        <view style="padding: 2upx"></view>
                    </view>
                    <view class="space-two flex-row" v-if="item.sendStatus === 1 || item.sendStatus === 9">
                        <view style="flex: 1;background-color: transparent;color: transparent;width: 100%;height: 100%"
                              @click="toPackageDetail(item)">
                            查看详情
                        </view>
                        <view>
                            <view class="auth button-padding flex-row" v-if="item.sendStatus === 1"
                                  @click="scanQr(item.id)">
                                <u-icon name="scan" color="#FFFFFF" size="17px"></u-icon>
                                扫码开柜
                            </view>
                            <view class="auth button-padding flex-row" v-if="item.sendStatus === 9"
                                  @click="toTrackList(item.waybillNo)">
                                查看物流
                            </view>
                        </view>
                    </view>
                </view>
                <view class="scroll-item" v-else-if="type ==='zc'">
                    <view class="space-two">
                        <view class="cabinet-name">{{ getItemTitle(item) }}</view>
                        <view class="mini-label" v-if="item.hasOutbound === 0"> 取件码<span
                                class="check-code">{{ item.checkCode.replace(/(\d{4})(?=\d)/g, '$1 ') }}</span>
                        </view>
                        <view class="label" v-else-if="item.hasOutbound === 1"> 已取</view>
                    </view>
                    <view style="padding: 6upx"></view>
                    <view class="space-two">
                        <view class="label">订单号</view>
                        <view class="label">{{ item.orderNo }}</view>
                    </view>
                    <view class="space-two">
                        <view class="label">存件人</view>
                        <view class="label">{{ item.inboundUserMobile }}</view>
                    </view>
                    <view class="space-two">
                        <view class="label">取件人</view>
                        <view class="label">{{ item.receiverMobile }}</view>
                    </view>
                    <view class="space-two">
                        <view class="label">存放时间</view>
                        <view class="label">{{ item.serviceDurationDesc }}</view>
                    </view>
                    <view class="space-two" v-if="item.storeType === 1">
                        <view class="label">格口类型</view>
                        <view class="label">{{ getBoxTypeName(item.cabinetBoxType) }}</view>
                    </view>
                    <view class="space-two" v-if="item.storeType === 1">
                        <view class="label">格口号</view>
                        <view class="label">{{ item.cabinetBoxLabel }}</view>
                    </view>
                    <view v-if=" item.hasOutbound === 1" class="space-two">
                        <view class="label">取件码</view>
                        <view class="label">{{ item.checkCode }}</view>
                    </view>
                    <view style="padding: 6upx"></view>
                    <view class="space-one-end">
                        <view class="fee-un-pay" v-if="item.hasOutbound === 0 && item.predictPrice > 0">
                            预估费用￥{{ (item.predictPrice / 1000).toFixed(2) }}
                        </view>
                        <view class="fee-al-pay" v-if="item.hasOutbound === 1 && item.outboundUserFee > 0">
                            已收费￥{{ (item.outboundUserFee / 1000).toFixed(2) }}
                        </view>
                    </view>
                    <view class="space-two flex-row" v-if="packageType === 'UNPICK'">
                        <view></view>
                        <view class="auth button-padding flex-row"
                              @click="orderCancel(item)">
                            取消订单
                        </view>
                    </view>
                </view>
            </view>
            <u-loadmore :status="loadStatus" margin-top="20" bg-color="#f6f6f6" font-size="13"/>
        </view>
        <view class="no-result" v-else>
        </view>
    </scroll-view>
</view>
