.container{
  position: relative;
  width: 750upx;
  padding-top: 110upx;
  height: 100vh;
  .top{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }
  .bar{
    width: 100%;
    height: 180upx;
    padding-top: 5upx;
    background-color: #ffffff;
  }

  .num-view{
    width: 750upx;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    height: 58upx;
    background-color: #ffffff;
    .item{
      flex: 1;
      color: $uni-color-error;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      .number-bar{
        padding: 1upx 12upx 1upx 12upx;
        display: flex;
        justify-content: center;
        align-content: center;
        background: $config-color;
        border-radius: 50upx;
        margin-bottom: 20upx;
      }
      .number{
        font-size: 22upx;
        font-weight: normal;
        color: white;
      }
      .label{
        font-size: 33upx;
        color: #333333;
      }
      .active{
        border-bottom: 2px solid $config-color;
      }
    }
  }

  .filter{
    display: flex;
    flex-direction: row;
    padding-left: 20upx;
    margin-top: 20upx;
    margin-bottom: 20upx;
    .filer-item{
      width: 170upx;
      height: 56upx;
      border-radius: 32upx;
      background: #F7F7F7;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 26upx;
      color: #B5B5B5;
      border: 1px solid #B5B5B5;
      &:first-child {
        margin-right: 20upx;
      }
    }
    .active{
      border: 1px solid $config-color;
      color: $config-color;
    }
  }

  .scroll-view{
    position: absolute;
    top: 285upx;
    //position: relative;
    //top: 175upx;
    left: 0;
    right: 0;
    bottom: 0;
    padding-top: 10upx;
    box-sizing: border-box;
  }
  .scroll-view2 {
    position: absolute;
    top: 185upx;
    //position: relative;
    //top: 175upx;
    left: 0;
    right: 0;
    bottom: 0;
    padding-top: 10upx;
    box-sizing: border-box;
  }

  .scroll-item{
    width: 690upx;
    margin-left: 30upx;
    max-height: 448upx;
    background: #FFFFFF;
    margin-bottom: 20upx;
    border-radius: 10upx;
    padding:  24upx 30upx;
    box-sizing: border-box;
  }
  .address-item {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;

    image {
      width: 50upx;
      height: 50upx;
    }

    .address-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-left: 20upx;

      .name {
        font-size: 28upx;
        font-weight: 400;
        color: #000000;
        max-width: 400upx;
        flex-wrap: wrap;
      }

      .info {
        margin-top: 3upx;
        font-size: 22upx;
        font-weight: 400;
        color: #999999;
        overflow: hidden;
        max-width: 490upx;
        text-overflow: ellipsis;
        word-wrap: normal;
        white-space: pre-wrap;
      }
    }

  }

  .border {
    width: 630upx;
    height: 1upx;
    margin: 12upx 0;
    background-color: #EEEEED;
  }
  .space-two {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  .space-one-end {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
  }
  .space-one-start {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }
  .cabinet-name{
    font-size: 30upx;
    font-weight: 500;
    max-width: 345upx;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    color: #333333;
  }
  .mini-label{
    font-size: 24upx;
    vertical-align: middle;
    font-weight: 400;
    color: #999999;
  }
  .check-code{
    margin-left: 20upx;
    font-size: 36upx;
    font-weight: 500;
    color: $config-color;
  }
  .label{
    font-size: 28upx;
    font-weight: 400;
    color: #585858;
  }
  .fee-un-pay{
    font-size: 28upx;
    font-weight: 400;
    color: $config-color;
  }
  .fee-al-pay{
    font-size: 28upx;
    font-weight: 400;
    color: #000000;
  }
}

.bottom2{
  position: absolute;
  bottom: 50upx;
  left: 0;
  right: 0;
  height: 75upx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99;
  .bottom-btn{
    width: 248upx;
    height: 76upx;
    background: $config-color;
    box-shadow: 0upx 0upx 26upx 0upx $config-color;
    border-radius: 38upx;
    display: flex;
    justify-content: center;
    align-items: center;
    .text{
      font-size: 28upx;
      font-weight: 400;
      color: #FFFFFF;
    }
  }
}
