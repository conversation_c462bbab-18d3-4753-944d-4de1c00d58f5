<script lang="ts">
import 'reflect-metadata';
import SearchInput from "@/components/search-input/index.vue"
import {Component, Mixins, Watch} from 'vue-property-decorator';
import {getAllList, getSendList} from '@/model/waybills'
import {payDto, SearchDto, userDto, waybillInfo} from '@/utils/types';
import {Cache, Modal} from '@/utils';
import Config from "@/utils/config";
import config from "@/utils/config";
import {CommonMixins} from "@/mixins/commonMixin";
import {ImageMixins} from "@/mixins/ImageMixins";
import {PackageMixins} from "@/mixins/packageMixin";
import {checkExpireTime, removeOuter} from "@/utils/core/uni";
import {getUserInfo} from '@/model/login';
import {getScanCodeHostCode} from "@/model/cabinet";
import {orderCancel} from "@/model/pay";

@Component({
  name: 'Index',
  components: {
    SearchInput
  }
})
export default class Index extends Mixins(CommonMixins, ImageMixins, PackageMixins) {
  private iconColor = ''
  private keyword: string = ''
  private type: string = 'qj'
  private packageType: string = 'UNPICK'
  private list: waybillInfo[] = []
  private current: number = 1
  private total: number = 0
  private pH: number = 0
  private navHeight: number = 0
  private loadStatus: string = 'loadmore'
  private scanResult: any = ''
  private user: userDto = {id: '', phone: ''}
  private show: boolean = false

  get brandName() {
    return function (val: string) {
      return Config.EXPRESS[val]
    }
  }

  get isAuthMobile() {
    return this.user.phone != '' && this.user.phone != null
  }

  changeTab(type: string) {
    this.packageType = 'UNPICK'
    this.current = 1
    this.total = 0
    this.type = type
    this.list = []
    this.getList()
  }

  // 取消订单
  async orderCancel(item: payDto) {
    this.show = true
    let info = {
      cabinetLocationCode: item.cabinetLocationCode,
      orderId: item.orderId,
      hostIndex: Cache.getItem('hostIndex')
    }
    let res = await orderCancel(info)
    if (res) {
      await this.getList()
    }
    this.show = false
  }

  changePackageType(type: string) {
    this.current = 1
    this.total = 0
    this.packageType = type
    this.getList()
  }

  getItemTitle(item: waybillInfo) {
    if (item.storeType == 2) {
      return item.shopName
    }
    return item.cabinetLocationName
  }

  async getUserInfo(isForce = false) {
    let user: any = await Cache.getItem('user');
    if (!user) {
      user = await getUserInfo();
    }
    if (isForce) {
      user = await getUserInfo();
    }
    this.user = user
    Cache.setItem('user', user)
  }

  getKeepTime(time: string) {
    let hours: number = Math.ceil((new Date().getTime() - new Date(time.replace(/-/g, '/')).getTime()) / 1000 / 3600)
    return hours >= 24 ? Math.floor(hours / 24) + '天' + hours % 24 : hours
  }

  @Watch('cabinetBoxIsOpen')
  getCabinetBoxIsOpen(val: string, oldVal: string) {
    if (val) {
      this.resetBox()
      this.toPackageDetail({id: this.paidOrderId})
    }
  }

  async getList() {
    
    let typeMap: any = {
      'qj': 1,
      'jj': 3,
      'zc': 2
    }
    let outboundMap: any = {
      'ALL': '',
      'UNPICK': false
    }
    let info: SearchDto = {
      current: this.current,
      size: 10
    }
    if (this.type === 'qj' || this.type === 'zc') {
      info.orderType = typeMap[this.type]
      info.hasOutbound = outboundMap[this.packageType]
    } else {

    }
    
    if (this.keyword !== '' && this.keyword !== null) {
      info.checkCode = this.keyword
    }
    let res = null
    
    if (this.type === 'qj' || this.type === 'zc') {
      res = await getAllList(info)
    } else {
      res = await getSendList(info)
    }
    if (res) {
      this.total = res.total * 1
      if (this.current === 1) {
        this.list = res.records
      } else {
        this.list = this.list.concat(res.records)
      }
      if (this.list.length === this.total) {
        this.loadStatus = 'noMore'
      } else {
        this.loadStatus = 'loadmore'
      }
      // console.log(this.list,'---data---')
    }
  }


  scan() {
    uni.scanCode({
          success: res => {
            if (res.path) {
              this.scanWxAppletCode(res.path)
            } else if (res.result) {
              // 扫码公众号静态码
              this.scanWxOfficialAccountCode(res.result)
            } else {
              Modal.alert('请扫描正确的柜机二维码')
            }
          },
          fail: err => {
            console.log(err);
            // Modal.alert('扫码失败')
          }
        }
    )
  }

  async scanWxOfficialAccountCode(result: string) {
    if (!result) return
    if (result.startsWith('http://weixin.qq.com/')) {
      const res = await getScanCodeHostCode({data: result})
      if (res) {
        Cache.setItem('hostIndex', res.hostIndex)
        const info = {
          cabinetLocationCode: res.code
        }
        uni.navigateTo({url: '/homePages/receive/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
      }
    }
  }

  scanWxAppletCode(path: string) {
    if (!path) {
      return
    }
    let cid = ''
    let code = ''
    let type = ''
    let hostIndex = ''
    let params: any[] = decodeURIComponent(path.replace('pages/home/<USER>/index?scene=', '')).split(',')
    params.forEach((item) => {
      if (item.startsWith('c')) {
        cid = item.split('c-')[1]
        Cache.setItem('cabinetLocationId', cid)
      }
      if (item.startsWith('t')) {
        type = item.split('t-')[1]
        Cache.setItem('scanType', type)
      }
      if (item.startsWith('l')) {
        code = item.split('l-')[1]
        Cache.setItem('cabinetLocationCode', code)
      }
      if (item.startsWith('i')) {
        hostIndex = item.split('i-')[1]
        Cache.setItem('hostIndex', hostIndex)
      }
    })
    this.goPackageList()
  }

  async goPackageList() {
    let isOverTime: boolean = checkExpireTime()
    if (isOverTime) {
      await Modal.alert('二维码失效,请重新扫描')
      removeOuter()
      return
    }
    let cabinetLocationCode = Cache.getItem('cabinetLocationCode') || ''
    let scanType = Cache.getItem('scanType') || ''
    let cabinetLocationId = Cache.getItem('cabinetLocationId') || ''
    if (this.isAuthMobile) {
      let isOverTime: boolean = checkExpireTime()
      if (isOverTime) {
        await Modal.alert('二维码失效,请重新扫描')
        removeOuter()
        return
      }
      const info = {
        cabinetLocationCode: cabinetLocationCode.toString().trim(),
        cabinetLocationId: cabinetLocationId.toString().trim()
      }
      if (cabinetLocationCode || cabinetLocationId) {
        removeOuter()
        if (scanType == 3) {
          uni.navigateTo({url: '/homePages/receive/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
        } else if (scanType == 2) {
          uni.navigateTo({url: '/homePages/send/packageList/packageList?info=' + encodeURIComponent(JSON.stringify(info))})
        } else {
          await Modal.alert('请扫描正确的柜机二维码')
        }
      }
    }
  }

  getBoxTypeName(type: number) {
    const map = Config.BoxLabel
    return map.get(type);
  }

  getTitle(type: string) {
    const map = new Map([['qj', '扫码取件'], ['jj', '扫码寄件'], ['zc', '扫码储物']])
    return map.get(type);
  }


  loadMore(e: any) {
    if (this.list.length < this.total) {
      if (this.loadStatus === 'loading') {
        return
      }
      this.loadStatus = 'loading'
      this.current++
      this.getList()
    }
  }

  resetCurrent() {
    this.current = 1
    this.loadStatus = 'loadmore'
  }

  onLoad() {
  }

  async onShow() {
    let color = config.CONFIG_COLOR
    this.iconColor = color
    await this.getList()
    await this.getUserInfo()
  }

  onReady() {
    // let that = this;
    // uni.getSystemInfo({ //调用uni-app接口获取屏幕高度
    //   success(res) { //成功回调函数
    //     that.pH = res.windowHeight //windoHeight为窗口高度，主要使用的是这个
    //     let titleH = uni.createSelectorQuery().select(".sv"); //想要获取高度的元素名（class/id）
    //     titleH.boundingClientRect(data => {
    //       console.log('data', data);
    //       let pH = that.pH;
    //       that.navHeight = pH   //计算高度：元素高度=窗口高度-元素距离顶部的距离（data.top）
    //       if (data.top) {
    //         that.navHeight -= data.top
    //       }
    //     }).exec()
    //   }
    // })
  }

  async onPullDownRefresh() {
    this.resetCurrent()
    await this.getList()
    uni.stopPullDownRefresh()
  }
}
</script>

<template src="./index.html"></template>
<style lang="scss" scoped src="./index.scss"></style>
