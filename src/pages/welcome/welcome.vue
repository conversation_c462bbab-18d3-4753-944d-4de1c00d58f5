<script lang="ts">
import 'reflect-metadata';
import {Component} from 'vue-property-decorator';
import {checkUser} from '@/utils/core/uni';
import {ImageMixins} from "@/mixins/ImageMixins";

@Component({
  name: 'Welcome'
})
export default class Welcome extends ImageMixins {
  private countDown: number = 1500
  private timer: any;

  onLoad() {

  }

  onShow() {
    let isAuthMobile: boolean = checkUser()
    this.timer = setInterval(() => {
      if (this.countDown > 0) {
        this.countDown -= 500
      } else if (this.countDown === 0) {
        if (!isAuthMobile) {
          uni.redirectTo({url: '/authPages/index/index'})
        } else {
          uni.switchTab({url: '/pages/home/<USER>/index'})
        }
      }
    }, 500)
  }

  onUnload() {
    this.timer && clearInterval(this.timer)
  }
}
</script>

<template src="./welcome.html"></template>
<style src="./welcome.scss" lang="scss" scoped></style>
