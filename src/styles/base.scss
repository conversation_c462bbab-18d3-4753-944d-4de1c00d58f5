/*每个页面公共css */
/* uni-app默认全局使用flex布局。因为flex布局有利于跨更多平台，尤其是采用原生渲染的平台。如不了解flex布局，请参考http://www.w3.org/TR/css3-flexbox/。如不使用flex布局，请删除或注释掉本行。*/
body,
page {
  display: flex;
  overflow-x: hidden;
  font-family: PingFangSC-Light, helvetica, 'Heiti SC', sans-serif;
  background: #f8f8f8;
  height: 100%;
  font-size: 28upx;
}

.container {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* #ifdef MP-BAIDU */
page {
  width: 100%;
  height: 100%;
  display: block;
}

swan-template {
  width: 100%;
  min-height: 100%;
  display: flex;
}

/* #endif */

.bg-color-white {
  background-color: white;
}

.p10 {
  padding: 0 20upx;
}
.pl5 {
  padding-left: 5upx;
}
.pl15 {
  padding-left: 15upx;
}
.pb10 {
  padding-bottom: 10upx;
}
.mt10 {
  margin-top: 10upx;
}
.align-center{
  align-items: center;
}
.primary-color {
  color:  $config-color;
}
.fw600{
  font-weight: 600;
}
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.flex-space {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.no-result {
  height: 650upx;
  background-image: url('~@/static/img/home/<USER>/noResult.png');
  background-size: 222upx 178upx;
  background-repeat: no-repeat;
  display: flex;
  background-position:center;
  justify-content: center;
  align-items: flex-end;

  .text {
    font-size: 28upx;
    color: #a4a4a4;
    margin-bottom: 80upx;
  }
}

.no-printTemp {
  width: 100%;
  height: 270upx;
  background: url('data:image/png;base64,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') center center no-repeat;
  background-size: 330upx 220upx;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  color: #bdbdbd;
}

.doc-title {
  position: relative;
  padding: 0 16upx;
  font-size: 26upx;
  height: 72upx;
  line-height: 76upx;
  color: #666;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4upx;

  .btn {
    padding: 0 25upx;
    line-height: 60upx;
    text-align: center;
    height: 60upx !important;
    color: #ffffff;
    font-size: 24upx;
    background-color: #397ad8;
    border-radius: 8upx;
  }
}

.doc-title .iconfont {
  margin-right: 6upx;
}

.doc-title::after {
  content: " ";
  position: absolute;
  left: 16upx;
  bottom: 5upx;
  height: 4upx;
  width: 30upx;
  background-color: #007dd4;
}

.zan-panel {
  margin-top: 10upx !important;
}

.qrCode {
  background: url("data:image/png;base64,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") no-repeat;
  background-size: cover;
}

.barCode {
  background: url("data:image/png;base64,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") no-repeat;
  background-size: cover;
}

.full-width {
  flex: 1;
}

.margin-left-10 {
  margin-left: 10upx;
}
.m20 {
  margin: 20upx;
}

.mr20 {
  margin-right: 20upx;
}

.mr10 {
  margin-right: 10upx;
}
.ml20 {
  margin-left: 20upx;
}
.ml30 {
  margin-left: 30upx;
}
.btn-hover{
  opacity: .7 !important;
}

.advertisement{
  background-color: #f8f8f8;
  width: 100%;
  position: fixed;
  bottom: 0upx;
}

.bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
}
.bottom-padding{
  padding: 0 30uxp;
}

.btn {
  width: 690upx;
  height: 90upx;
  line-height: 90upx;
  border-radius: 45upx;
  text-align: center;
  font-size: $uni-font-size-sm;
}
.sqrt-btn{
  width: 300upx;
  height: 90upx;
  line-height: 90upx;
  border-radius: 10upx;
  text-align: center;
  font-size: $uni-font-size-sm;
}

.btn-padding {
  margin-bottom: 40upx;
  margin-top: 10upx;
}
.auth {
  color: #ffffff;
  background-color: $config-color;
  margin-bottom: 40upx;
  margin-top: 10upx;
}
.button-padding{
  padding: 10upx 20upx;
  border-radius: 10upx;
  margin: 0;
}
.grey {
  color: #ffffff;
  background-color: #323436;
  margin-bottom: 40upx;
  margin-top: 10upx;
}
.slot-btn {
    border-left: 2upx solid #EEEEEE;
    padding-left: 25upx;
    margin-left: 15upx;
    text {
      font-size: 40upx;
      color: $config-color;
    }
  }

.btn-disable{
  background: #FFE6DE;
}
