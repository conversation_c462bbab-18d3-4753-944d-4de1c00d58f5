.zan-actionsheet {
    background-color: #f8f8f8
}

.zan-actionsheet__mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    background: rgba(0, 0, 0, .7);
    display: none
}

.zan-actionsheet__container {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #f8f8f8;
    transform: translate3d(0, 50%, 0);
    transform-origin: center;
    transition: all .2s ease;
    z-index: 11;
    opacity: 0;
    visibility: hidden
}

.zan-actionsheet__btn.zan-btn {
    height: 50px;
    line-height: 50px;
    margin-bottom: 0
}

.zan-actionsheet__btn.zan-btn::after {
    border-width: 0;
    border-bottom-width: 1px
}

.zan-actionsheet__btn.zan-btn:last-child::after {
    border-bottom-width: 0
}

.zan-actionsheet__subname {
    margin-left: 2px;
    font-size: 12px;
    color: #666
}

.zan-actionsheet__footer {
    margin-top: 10px
}

.zan-actionsheet__btn.zan-btn--loading .zan-actionsheet__subname {
    color: transparent
}

.zan-actionsheet--show .zan-actionsheet__container {
    opacity: 1;
    transform: translate3d(0, 0, 0);
    visibility: visible
}

.zan-actionsheet--show .zan-actionsheet__mask {
    display: block
}

.zan-badge {
    position: relative
}

.zan-badge__count {
    position: absolute;
    top: -16px;
    right: 0;
    height: 1.6em;
    min-width: 1.6em;
    line-height: 1.6;
    padding: 0 .4em;
    font-size: 20px;
    border-radius: .8em;
    background: #f44;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    transform: translateX(50%) scale(.5);
    transform-origin: center;
    z-index: 10;
    box-shadow: 0 0 0 2px #fff;
    box-sizing: border-box
}

.zan-btn {
    position: relative;
    color: #333;
    background-color: #fff;
    margin-bottom: 10px;
    padding-left: 15px;
    padding-right: 15px;
    border-radius: 2px;
    font-size: 16px;
    line-height: 45px;
    height: 45px;
    box-sizing: border-box;
    text-decoration: none;
    text-align: center;
    vertical-align: middle
}

.zan-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    transform: scale(.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    border: 1px solid #e5e5e5;
    border-radius: 4px
}

.zan-btns {
    margin: 15px
}

.zan-btn--primary {
    color: #fff;
    background-color: #007dd4
}

.zan-btn--primary::after {
    border-color: #007dd4
}

.zan-btn--warn {
    color: #fff;
    background-color: #f85
}

.zan-btn--warn::after {
    border-color: #f85
}

.zan-btn--danger {
    color: #fff;
    background-color: #f44
}

.zan-btn--danger::after {
    border-color: #e33
}

.zan-btn--small {
    display: inline-block;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 0
}

.zan-btn--mini {
    display: inline-block;
    line-height: 21px;
    height: 22px;
    font-size: 10px;
    margin-right: 5px;
    margin-bottom: 0;
    padding-left: 5px;
    padding-right: 5px
}

.zan-btn--large {
    border-radius: 0;
    margin-bottom: 0;
    border: none;
    line-height: 50px;
    height: 50px
}

.zan-btn--plain.zan-btn {
    background-color: transparent
}

.zan-btn--plain.zan-btn--primary {
    color: #06bf04
}

.zan-btn--plain.zan-btn--warn {
    color: #f60
}

.zan-btn--plain.zan-btn--danger {
    color: #f44
}

.button-hover {
    opacity: .9
}

.zan-btn--loading {
    color: transparent;
    opacity: 1
}

.zan-btn--loading::before {
    position: absolute;
    left: 50%;
    top: 50%;
    content: ' ';
    width: 16px;
    height: 16px;
    margin-left: -8px;
    margin-top: -8px;
    border: 3px solid #e5e5e5;
    //border-color: #666 #e5e5e5 #e5e5e5 #e5e5e5;
    border-radius: 8px;
    box-sizing: border-box;
    animation: btn-spin .6s linear;
    animation-iteration-count: infinite
}

.zan-btn--danger.zan-btn--loading::before, .zan-btn--primary.zan-btn--loading::before, .zan-btn--warn.zan-btn--loading::before {
    border-color: #fff rgba(0, 0, 0, .1) rgba(0, 0, 0, .1) rgba(0, 0, 0, .1)
}

@keyframes btn-spin {
    0% {
        transform: rotate(0)
    }
    100% {
        transform: rotate(360deg)
    }
}

.zan-btn.zan-btn--disabled {
    color: #999 !important;
    background: #f8f8f8 !important;
    border-color: #e5e5e5 !important;
    cursor: not-allowed !important;
    opacity: 1 !important
}

.zan-btn.zan-btn--disabled::after {
    border-color: #e5e5e5 !important
}

.zan-btn--last-child, .zan-btn:last-child {
    margin-bottom: 0;
    margin-right: 0
}

.zan-capsule {
    display: inline-block;
    font-size: 12px;
    vertical-align: middle;
    line-height: 19px;
    transform: scale(.83)
}

.zan-capsule__left, .zan-capsule__right {
    display: inline-block;
    line-height: 17px;
    height: 19px;
    vertical-align: middle;
    box-sizing: border-box
}

.zan-capsule__left {
    padding: 0 2px;
    color: #fff;
    background: #999;
    border-radius: 2px 0 0 2px;
    border: 1 rpx solid #999
}

.zan-capsule__right {
    padding: 0 5px;
    color: #999;
    border-radius: 0 2px 2px 0;
    border: 1 rpx solid #999
}

.zan-capsule--danger .zan-capsule__left {
    color: #fff;
    background: #f24544;
    border-color: #f24544
}

.zan-capsule--danger .zan-capsule__right {
    color: #f24544;
    border-color: #f24544
}

.zan-card {
    margin-left: 0;
    width: auto;
    padding: 5px 15px;
    overflow: hidden;
    position: relative;
    font-size: 14px
}

.zan-card__thumb {
    width: 90px;
    height: 90px;
    float: left;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
    background-size: cover
}

.zan-card__img {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%
}

.zan-card__detail {
    margin-left: 100px;
    width: auto;
    position: relative
}

.zan-card__detail-row {
    overflow: hidden;
    line-height: 20px;
    min-height: 20px;
    margin-bottom: 3px;
}

.zan-card__right-col {
    float: right
}

.zan-card__left-col {
    margin-right: 80px
}

.zan-cell {
    position: relative;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    line-height: 1.4;
    font-size: 14px
}

.zan-cell::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    transform: scale(.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    border: 0 solid #e5e5e5;
    border-bottom-width: 1px;
    left: 15px;
    right: 0
}

.zan-cell__icon {
    margin-right: 5px
}

.zan-cell__bd {
    flex: 1
}

.zan-cell__text {
    line-height: 24px;
    font-size: 14px
}

.zan-cell__desc {
    line-height: 1.2;
    font-size: 12px;
    color: #666
}

.zan-cell__ft {
    position: relative;
    text-align: right;
    color: #666
}

.zan-cell__no-pading {
    padding: 0
}

.zan-cell__no-pading .zan-cell__bd_padding {
    padding: 12px 0 12px 15px
}

.zan-cell__no-pading .zan-cell__bd_padding .zan-form__input {
    height: 26px
}

.zan-cell__no-pading .zan-cell__ft_padding {
    padding: 12px 15px 12px 0
}

.zan-cell--last-child::after, .zan-cell:last-child::after {
    display: none
}

.zan-cell--access .zan-cell__ft {
    padding-right: 13px
}

.zan-cell--access .zan-cell__ft::after {
    position: absolute;
    top: 50%;
    right: 2px;
    content: " ";
    display: inline-block;
    height: 6px;
    width: 6px;
    border-width: 2px 2px 0 0;
    border-color: #c8c8c8;
    border-style: solid;
    transform: translateY(-50%) matrix(.71, .71, -.71, .71, 0, 0)
}

.zan-cell--switch {
    padding-top: 6px;
    padding-bottom: 6px
}

.zan-col {
    float: left;
    box-sizing: border-box;
    width: 0
}

.zan-col-1 {
    width: 4.16667%
}

.zan-col-offset-1 {
    margin-left: 4.16667%
}

.zan-col-2 {
    width: 8.33333%
}

.zan-col-offset-2 {
    margin-left: 8.33333%
}

.zan-col-3 {
    width: 12.5%
}

.zan-col-offset-3 {
    margin-left: 12.5%
}

.zan-col-4 {
    width: 16.66667%
}

.zan-col-offset-4 {
    margin-left: 16.66667%
}

.zan-col-5 {
    width: 20.83333%
}

.zan-col-offset-5 {
    margin-left: 20.83333%
}

.zan-col-6 {
    width: 25%
}

.zan-col-offset-6 {
    margin-left: 25%
}

.zan-col-7 {
    width: 29.16667%
}

.zan-col-offset-7 {
    margin-left: 29.16667%
}

.zan-col-8 {
    width: 33.33333%
}

.zan-col-offset-8 {
    margin-left: 33.33333%
}

.zan-col-9 {
    width: 37.5%
}

.zan-col-offset-9 {
    margin-left: 37.5%
}

.zan-col-10 {
    width: 41.66667%
}

.zan-col-offset-10 {
    margin-left: 41.66667%
}

.zan-col-11 {
    width: 45.83333%
}

.zan-col-offset-11 {
    margin-left: 45.83333%
}

.zan-col-12 {
    width: 50%
}

.zan-col-offset-12 {
    margin-left: 50%
}

.zan-col-13 {
    width: 54.16667%
}

.zan-col-offset-13 {
    margin-left: 54.16667%
}

.zan-col-14 {
    width: 58.33333%
}

.zan-col-offset-14 {
    margin-left: 58.33333%
}

.zan-col-15 {
    width: 62.5%
}

.zan-col-offset-15 {
    margin-left: 62.5%
}

.zan-col-16 {
    width: 66.66667%
}

.zan-col-offset-16 {
    margin-left: 66.66667%
}

.zan-col-17 {
    width: 70.83333%
}

.zan-col-offset-17 {
    margin-left: 70.83333%
}

.zan-col-18 {
    width: 75%
}

.zan-col-offset-18 {
    margin-left: 75%
}

.zan-col-19 {
    width: 79.16667%
}

.zan-col-offset-19 {
    margin-left: 79.16667%
}

.zan-col-20 {
    width: 83.33333%
}

.zan-col-offset-20 {
    margin-left: 83.33333%
}

.zan-col-21 {
    width: 87.5%
}

.zan-col-offset-21 {
    margin-left: 87.5%
}

.zan-col-22 {
    width: 91.66667%
}

.zan-col-offset-22 {
    margin-left: 91.66667%
}

.zan-col-23 {
    width: 95.83333%
}

.zan-col-offset-23 {
    margin-left: 95.83333%
}

.zan-col-24 {
    width: 100%
}

.zan-col-offset-24 {
    margin-left: 100%
}

.zan-c-red {
    color: #f44 !important
}

.zan-c-gray {
    color: #c9c9c9 !important
}

.zan-c-gray-dark {
    color: #999 !important
}

.zan-c-gray-darker {
    color: #666 !important
}

.zan-c-black {
    color: #333 !important
}

.zan-c-blue {
    color: #38f !important
}

.zan-c-green {
    color: #06bf04 !important
}

.zan-dialog--container {
    position: fixed;
    top: 45%;
    left: 50%;
    width: 80%;
    height: 0;
    font-size: 16px;
    overflow: hidden;
    transition: all .2s linear;
    border-radius: 4px;
    background-color: #fff;
    transform: translate3d(-50%, -50%, 0);
    color: #333;
    opacity: 0
}

.zan-dialog--mask {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, .6);
    transition: .3s;
    display: none
}

.zan-dialog__header {
    padding: 15px 0 0;
    text-align: center
}

.zan-dialog__content {
    position: relative;
    padding: 15px 20px;
    line-height: 1.5;
    min-height: 40px
}

.zan-dialog__content::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    transform: scale(.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    border: 0 solid #e5e5e5;
    border-bottom-width: 1px
}

.zan-dialog__content--title {
    color: #999;
    font-size: 14px
}

.zan-dialog__footer {
    overflow: hidden
}

.zan-dialog__button {
    line-height: 50px;
    height: 50px;
    padding: 0 5px;
    border-radius: 0;
    margin-bottom: 0
}

.zan-dialog__button::after {
    border-width: 0;
    border-radius: 0
}

.zan-dialog--show .zan-dialog--container {
    opacity: 1;
    height: auto
}

.zan-dialog--show .zan-dialog--mask {
    display: block
}

.zan-dialog__footer--horizon {
    display: flex
}

.zan-dialog__footer--horizon .zan-dialog__button {
    flex: 1
}

.zan-dialog__footer--horizon .zan-dialog__button::after {
    border-right-width: 1px
}

.zan-dialog__footer--horizon .zan-dialog__button:last-child::after {
    border-right-width: 0
}

.zan-dialog__footer--vertical .zan-dialog__button {
    flex: 1
}

.zan-dialog__footer--vertical .zan-dialog__button::after {
    border-bottom-width: 1px
}

.zan-dialog__footer--vertical .zan-dialog__button:last-child::after {
    border-bottom-width: 0
}

.zan-field {
    padding: 7px 15px;
    color: #333
}

.zan-field--wrapped {
    margin: 0 15px;
    background-color: #fff
}

.zan-field--wrapped::after {
    left: 0;
    border-width: 1px;
    border-radius: 4px
}

.zan-field.zan-field--wrapped::after {
    display: block
}

.zan-field--wrapped + .zan-field--wrapped {
    margin-top: 10px
}

.zan-field--error {
    color: #f40
}

.zan-field--wrapped.zan-field--error::after {
    border-color: #f40
}

.zan-field__title {
    min-width: 65px;
    padding-right: 10px
}

.zan-field__input {
    flex: 1;
    line-height: 1.6;
    padding: 4px 0;
    min-height: 22px;
    height: auto;
    font-size: 14px
}

.zan-field__placeholder {
    font-size: 14px;
}

.zan-field__input--right {
    text-align: right
}

.zan-pull-left {
    float: left
}

.zan-pull-right {
    float: right
}

.zan-center {
    text-align: center
}

.zan-right {
    text-align: right
}

.zan-text-deleted {
    text-decoration: line-through
}

.zan-font-8 {
    font-size: 8px
}

.zan-font-10 {
    font-size: 10px
}

.zan-font-12 {
    font-size: 12px
}

.zan-font-14 {
    font-size: 14px
}

.zan-font-16 {
    font-size: 16px
}

.zan-font-18 {
    font-size: 18px
}

.zan-font-20 {
    font-size: 20px
}

.zan-font-22 {
    font-size: 22px
}

.zan-font-24 {
    font-size: 22px
}

.zan-font-30 {
    font-size: 30px
}

.zan-font-bold {
    font-weight: 700
}

.zan-arrow {
    position: absolute;
    right: 15px;
    top: 50%;
    display: inline-block;
    height: 6px;
    width: 6px;
    border-width: 2px 2px 0 0;
    border-color: #c8c8c8;
    border-style: solid;
    transform: translateY(-50%) matrix(.71, .71, -.71, .71, 0, 0)
}

.zan-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal
}

.zan-ellipsis--l2 {
    max-height: 40px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical
}

.zan-ellipsis--l3 {
    max-height: 60px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical
}

.zan-clearfix {
    zoom: 1
}

.zan-clearfix::after {
    content: '';
    display: table;
    clear: both
}

.zan-hairline, .zan-hairline--bottom, .zan-hairline--left, .zan-hairline--right, .zan-hairline--surround, .zan-hairline--top, .zan-hairline--top-bottom {
    position: relative
}

.zan-hairline--bottom::after, .zan-hairline--left::after, .zan-hairline--right::after, .zan-hairline--surround::after, .zan-hairline--top-bottom::after, .zan-hairline--top::after, .zan-hairline::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    transform: scale(.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    border: 0 solid #e5e5e5
}

.zan-hairline--top::after {
    border-top-width: 1px
}

.zan-hairline--left::after {
    border-left-width: 1px
}

.zan-hairline--right::after {
    border-right-width: 1px
}

.zan-hairline--bottom::after {
    border-bottom-width: 1px
}

.zan-hairline--top-bottom::after {
    border-width: 1px 0
}

.zan-hairline--surround::after {
    border-width: 1px
}

.zan-noticebar {
    color: #f60;
    padding: 9px 10px;
    font-size: 12px;
    line-height: 1.5;
    background-color: #fff7cc
}

.zan-panel {
    position: relative;
    background: #fff;
    margin-top: 10px;
    overflow: hidden
}

.zan-panel::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    transform: scale(.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    border: 0 solid #e5e5e5;
    border-top-width: 1px;
    border-bottom-width: 1px
}

.zan-panel-title {
    font-size: 14px;
    line-height: 1;
    color: #999;
    padding: 20px 15px 0 15px
}

.zan-panel--without-margin-top {
    margin-top: 0
}

.zan-panel--without-border::after {
    border: 0 none
}

.zan-popup {
    visibility: hidden
}

.zan-popup--show {
    visibility: visible
}

.zan-popup__mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    background: rgba(0, 0, 0, .7);
    display: none
}

.zan-popup__container {
    position: fixed;
    left: 50%;
    top: 50%;
    background: #fff;
    transform: translate3d(-50%, -50%, 0);
    transform-origin: center;
    transition: all .4s ease;
    z-index: 11;
    opacity: 0
}

.zan-popup--show .zan-popup__container {
    opacity: 1
}

.zan-popup--show .zan-popup__mask {
    display: block
}

.zan-popup--left .zan-popup__container {
    left: 0;
    top: auto;
    transform: translate3d(-100%, 0, 0)
}

.zan-popup--show.zan-popup--left .zan-popup__container {
    transform: translate3d(0, 0, 0)
}

.zan-popup--right .zan-popup__container {
    right: 0;
    top: auto;
    left: auto;
    transform: translate3d(100%, 0, 0)
}

.zan-popup--show.zan-popup--right .zan-popup__container {
    transform: translate3d(0, 0, 0)
}

.zan-popup--bottom .zan-popup__container {
    top: auto;
    left: auto;
    bottom: 0;
    transform: translate3d(0, 100%, 0)
}

.zan-popup--show.zan-popup--bottom .zan-popup__container {
    transform: translate3d(0, 0, 0)
}

.zan-popup--top .zan-popup__container {
    top: 0;
    left: auto;
    transform: translate3d(0, -100%, 0)
}

.zan-popup--show.zan-popup--top .zan-popup__container {
    transform: translate3d(0, 0, 0)
}

.zan-row:after {
    content: "";
    display: table;
    clear: both
}

.zan-select__list .zan-select__radio {
    display: none
}

.zan-stepper {
    color: #666
}

.zan-stepper view {
    display: inline-block;
    line-height: 20px;
    padding: 5px 0;
    text-align: center;
    min-width: 40px;
    box-sizing: border-box;
    vertical-align: middle;
    font-size: 12px;
    border: 1 rpx solid #999
}

.zan-stepper .zan-stepper__minus {
    border-right: none;
    border-radius: 2px 0 0 2px
}

.zan-stepper .zan-stepper__text {
    border: 1 rpx solid #999;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    height: 30px;
    width: 40px;
    min-height: auto;
    font-size: 12px;
    line-height: 30px
}

.zan-stepper .zan-stepper__plus {
    border-left: none;
    border-radius: 0 2px 2px 0
}

.zan-stepper .zan-stepper--disabled {
    background: #f8f8f8;
    color: #bbb;
    border-color: #e8e8e8
}

.zan-stepper--small view {
    min-width: 36px;
    line-height: 18px
}

.zan-stepper--small .zan-stepper__text {
    width: 36px;
    line-height: 28px;
    height: 28px
}

.zan-steps--steps.zan-steps--5 .zan-steps__step {
    width: 25%
}

.zan-steps--steps.zan-steps--4 .zan-steps__step {
    width: 33%
}

.zan-steps--steps.zan-steps--3 .zan-steps__step {
    width: 50%
}

.zan-steps--steps .zan-steps__step {
    position: relative;
    float: left;
    padding-bottom: 25px;
    color: #b1b1b1
}

.zan-steps--steps .zan-steps__title {
    transform: translateX(-50%);
    font-size: 10px;
    text-align: center
}

.zan-steps--steps .zan-steps__icons {
    position: absolute;
    top: 30px;
    left: -10px;
    padding: 0 8px;
    background-color: #fff;
    z-index: 10
}

.zan-steps--steps .zan-steps__circle {
    display: block;
    position: relative;
    width: 5px;
    height: 5px;
    background-color: #e5e5e5;
    border-radius: 50%
}

.zan-steps--steps .zan-steps__line {
    position: absolute;
    left: 0;
    top: 32px;
    width: 100%;
    height: 1px;
    background-color: #e5e5e5
}

.zan-steps--steps .zan-steps__step--done {
    color: #333
}

.zan-steps--steps .zan-steps__step--done .zan-steps__line {
    background-color: #06bf04
}

.zan-steps--steps .zan-steps__step--done .zan-steps__circle {
    width: 5px;
    height: 5px;
    background-color: #09bb07
}

.zan-steps--steps .zan-steps__step--cur .zan-steps__icons {
    top: 25px;
    left: -14px
}

.zan-steps--steps .zan-steps__step--cur .zan-steps__line {
    background-color: #e5e5e5
}

.zan-steps--steps .zan-steps__step--first-child .zan-steps__title {
    margin-left: 0;
    transform: none;
    text-align: left
}

.zan-steps--steps .zan-steps__step--first-child .zan-steps__icons {
    left: -7px
}

.zan-steps--steps .zan-steps__step--last-child {
    position: absolute;
    right: 0;
    top: 0;
    text-align: right
}

.zan-steps--steps .zan-steps__step--last-child .zan-steps__title {
    transform: none;
    text-align: right
}

.zan-steps--steps .zan-steps__step--last-child .zan-steps__icons {
    left: auto;
    right: -6px
}

.zan-steps--steps .zan-steps__step--last-child .zan-steps__line {
    display: none
}

.zan-steps--steps .zan-steps__step--db-title {
    min-height: 29px
}

.zan-steps--steps .zan-steps__step--db-title .zan-steps__line {
    top: 45px
}

.zan-steps--steps .zan-steps__step--db-title .zan-steps__icons {
    top: 43px
}

.zan-steps--steps .zan-steps__step--db-title.zan-steps__step--cur .zan-steps__icons {
    top: 39px
}

.zan-steps--vsteps {
    color: #999;
    font-size: 14px
}

.zan-steps--vsteps .zan-steps__step {
    position: relative;
    padding: 15px 0
}

.zan-steps--vsteps .zan-steps__step--done {
    color: #007dd4
}

.zan-steps--vsteps .zan-steps__line {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 7px;
    width: 1px;
    background-color: #e5e5e5
}

.zan-steps--vsteps .zan-steps__title {
    display: inline-block;
    line-height: 20px;
    padding-left: 27px
}

.zan-steps--vsteps .zan-steps__title--desc {
    padding-left: 3px
}

.zan-steps--vsteps .zan-steps__icons {
    position: absolute;
    left: 7px;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    padding: 3px 0;
    background-color: #fff
}

.zan-steps--vsteps .zan-steps__circle {
    width: 5px;
    height: 5px;
    background-color: #cacaca;
    border-radius: 10px
}

.zan-steps--vsteps .zan-steps__step--done .zan-steps__circle {
    width: 5px;
    height: 5px;
    background-color: #09bb07
}

.zan-steps--vsteps .zan-steps__step--cur .zan-steps__circle {
    width: 13px;
    height: 13px;
    background: transparent url(https://b.yzcdn.cn/v2/image/wap/<EMAIL>);
    background-size: 13px 13px;
    border-radius: 0
}

.zan-steps--vsteps .zan-steps__icon--active {
    width: 13px;
    height: 13px
}

.zan-steps--vsteps .zan-steps__step--first-child .zan-steps__title::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 50%;
    left: 7px;
    width: 1px;
    background-color: #fff;
    z-index: 1
}

.zan-steps--vsteps .zan-steps__step--last-child .zan-steps__title::after {
    content: '';
    position: absolute;
    top: 50%;
    bottom: 0;
    left: 7px;
    width: 1px;
    background-color: #fff;
    z-index: 1
}

.zan-steps {
    position: relative
}

.zan-switch {
    position: relative;
    display: inline-block;
    width: 52px;
    height: 32px;
    vertical-align: middle;
    box-sizing: border-box;
    border-radius: 16px;
    background: #44db5e;
    border: 1px solid #44db5e
}

.zan-switch__circle {
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 30px;
    display: inline-block;
    background: #fff;
    border-radius: 15px;
    box-sizing: border-box;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, .1), 0 3px 1px 0 rgba(0, 0, 0, .05), 0 2px 2px 0 rgba(0, 0, 0, .1), 0 3px 3px 0 rgba(0, 0, 0, .05);
    transition: transform .35s cubic-bezier(.45, 1, .4, 1);
    z-index: 2
}

.zan-switch__bg {
    position: absolute;
    top: -1px;
    left: -1px;
    width: 52px;
    height: 32px;
    background: #fff;
    border-radius: 26px;
    display: inline-block;
    border: 1px solid #e5e5e5;
    box-sizing: border-box;
    transition: transform .35s cubic-bezier(.45, 1, .4, 1);
    transform: scale(0);
    transform-origin: 36px 16px
}

.zan-switch--on .zan-switch__circle {
    transform: translateX(20px)
}

.zan-switch--off .zan-switch__bg {
    transform: scale(1)
}

.zan-swtich--disabled {
    opacity: .4
}

.zan-switch__loading {
    position: absolute;
    left: 7px;
    top: 7px;
    width: 16px;
    height: 16px;
    background: url(https://img.yzcdn.cn/public_files/2017/02/24/9acec77d91106cd15b8107c4633d9155.png) no-repeat;
    background-size: 16px 16px;
    animation: zan-switch-loading .8s infinite linear
}

@keyframes zan-switch-loading {
    from {
        transform: rotate(0)
    }
    to {
        transform: rotate(360deg)
    }
}

.zan-tab {
    height: 45px
}

.zan-tab__bd {
    width: 750 rpx;
    display: flex;
    flex-direction: row;
    border-bottom: 1 rpx solid #e5e5e5;
    background: #fff
}

.zan-tab__bd--fixed {
    position: fixed;
    top: 0;
    z-index: 2
}

.zan-tab__item {
    flex: 1;
    display: inline-block;
    padding: 0 10px;
    line-height: 0;
    box-sizing: border-box;
    overflow: hidden;
    text-align: center
}

.zan-tab__title {
    display: inline-block;
    max-width: 100%;
    height: 44px;
    line-height: 44px;
    overflow: hidden;
    text-overflow: ellipsis;
    box-sizing: border-box;
    word-break: keep-all;
    font-size: 14px;
    color: #666
}

.zan-tab__item--selected .zan-tab__title {
    color: #f44;
    border-bottom: 2px solid #f44
}

.zan-tab__bd--scroll {
    display: block;
    white-space: nowrap
}

.zan-tab__bd--scroll .zan-tab__item {
    min-width: 80px
}

.zan-tag {
    display: inline-block;
    position: relative;
    box-sizing: border-box;
    line-height: 16px;
    padding: 0 5px;
    border-radius: 2px;
    font-size: 11px;
    background: #c9c9c9;
    text-align: center;
    color: #fff
}

.zan-tag::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    transform: scale(.5);
    transform-origin: 0 0;
    pointer-events: none;
    box-sizing: border-box;
    border: 0 solid #e5e5e5;
    border-width: 1px;
    border-radius: 4px
}

.zan-tag--plain {
    color: #c9c9c9;
    background: #fff
}

.zan-tag--primary {
    color: #fff;
    background-color: #007dd4
}

.zan-tag--primary::after {
    border-color: #007dd4
}

.zan-tag--primary.zan-tag--plain {
    color: #007dd4;
    background: #fff
}

.zan-tag--danger {
    color: #fff;
    background: #f44
}

.zan-tag--danger::after {
    border-color: #f44
}

.zan-tag--danger.zan-tag--plain {
    color: #f44;
    background: #fff
}

.zan-tag--warn {
    color: #fff;
    background: #f85
}

.zan-tag--warn::after {
    border-color: #f85
}

.zan-tag--warn.zan-tag--plain {
    color: #f85;
    background: #fff
}

.zan-tag--disabled {
    color: #999 !important;
    background: #e5e5e5
}

.zan-tag--disabled::after {
    border-color: #ccc
}

.zan-toast {
    position: fixed;
    top: 35%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    background: rgba(0, 0, 0, .7);
    color: #fff;
    font-size: 14px;
    line-height: 1.5em;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 10px 18px;
    text-align: center;
    border-radius: 4px;
    z-index: 100
}

.zan-toast--notitle {
    padding: 18px
}

.zan-toast__icon {
    width: 40px;
    height: 40px;
    line-height: 40px;
    margin: 0 auto;
    padding: 12px 15px;
    font-size: 38px;
    text-align: center
}

.zan-toast__icon-loading {
    line-height: 0
}

.zan-toast__icon-loading .zan-loading {
    width: 40px;
    height: 40px
}

.zan-toast__icon-image {
    background-size: 40px;
    background-position: center;
    background-repeat: no-repeat
}

.zan-toptips {
    display: block;
    position: fixed;
    -webkit-transform: translateZ(0) translateY(-100%);
    width: 100%;
    min-height: 32px;
    top: 0;
    line-height: 2.3;
    font-size: 14px;
    text-align: center;
    color: #fff;
    background-color: #e64340;
    z-index: 110;
    opacity: 0;
    transition: all .4s ease
}

.zan-toptips--show {
    -webkit-transform: translateZ(0) translateY(0);
    opacity: 1
}

.must{
    color:red;
}

.notHaveTo{
    color: black;
}
