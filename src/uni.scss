/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */
@import "~uview-ui/theme.scss";
/* 主题颜色  #FF5E24 #1871ee */

// $config-color:#1871ee; // 便利智能柜
$config-color:#FF5E24; // 熊猫智能柜


/* 行为相关颜色 */
$uni-color-primary: #FF6718;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color:#333;//基本色
$uni-text-color-inverse:#fff;//反色
$uni-text-color-grey:#999;//辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #767676;
$uni-text-color-disable:#c0c0c0;
$uni-text-color-default: #4b4b4b;
$uni-text-color-global: #3087D7;
$uni-text-color-red: #dd524d;

/* 背景颜色 */
$uni-bg-color-global: #3087D7;
$uni-bg-color: #f0f0f0;
$uni-bg-color-grey: #f6f6f7;
$uni-bg-color-green: #a1f9c2;
$uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color-grey: #f7f7f7;
$uni-border-color-dark: #eaeaea;
$uni-border-color-global: #3087D7;

/* 边框圆角 */
$uni-border-radius-base: 8upx;
$uni-border-radius-max: 16upx;

/* 文字尺寸 */
$uni-font-size-max: 34upx;
$uni-font-size-big: 32upx;
$uni-font-size-base: 30upx;
$uni-font-size-sm: 28upx;
$uni-font-size-min: 26upx;


$btnHeight: 80upx;



