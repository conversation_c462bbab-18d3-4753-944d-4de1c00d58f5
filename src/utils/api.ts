import { isAlipay } from '@/utils/core/uni';
class ApiClass {
    // 根据平台动态设置API路径
    get login() { return isAlipay() ? 'alimp.login' : 'wxmp.login'; }
    mock = 'wxmp.mock.login'
    get wempBindPhone() {return isAlipay() ? 'alimp.bind.mobile':'wxmp.bind.mobile' }   //支付宝手机号接口是alimp.bind.mobile
    get qrCodeDate() { return isAlipay()? 'alimp.qr.convert' : 'wxmp.qr.convert'; }
    bindGzh = 'wxmp.bind.gzh'
    cabinetList = 'wxmp.nearby.cabinet.location'
    userInfo = 'wxmp.info'
    orderList = 'wxmp.cabinet.order.list'
    orderListInShop ='wxmp.cabinet.order.list.in.shop'
    sendOrderList = 'wxmp.cabinet.order.send.list'
    outbound = 'wxmp.cabinet.outbound.waybill.order'
    allList = 'wxmp.order.list'
    sendList = 'wxmp.order.send.page'
    get payParams() { return isAlipay() ? 'alimp.pay.order.waybill' : 'wxmp.pay.order.waybill'; }
    orderIsPay = 'wxmp.order.paid'
    orderCancel='wxmp.order.cancel'
    canInbound = 'wxmp.cabinet.set.can.inbound'
    authRealName = 'wxmp.real.name.auth'
    locationApply = 'wxmp.cabinet.location.apply'
    scanCode = 'wxmp.scan'
    scanCodeHostCode ='wxmp.scan.host.code'
    getDefaultAddress = 'wxmp.default.address'
    addressList = 'wxmp.address.list'
    sysOrgList = 'wxmp.area.list'
    saveAddress = 'wxmp.save.address'
    extractExpress = 'wxmp.extract.express'
    deleteAddress = 'wxmp.address.remove'
    defaultAddress = 'wxmp.set.default.address'
    createOrder = 'wxmp.order.send.save'
    cancelOrder = 'wxmp.order.send.cancel'
    orderDetail = 'wxmp.order.send.get'
    get createPayOrder() { return isAlipay() ? 'alimp.cabinet.order.send.inbound' : 'wxmp.cabinet.order.send.inbound'; }
    checkPayOrder = 'wxmp.order.send.paid'
    openSendDoor = 'wxmp.cabinet.send.order.open.door'
    openBoxByOrder ='open.box.by.order'
    refundList = 'wxmp.order.refund.list'
    usableSummary = 'cabinet.usable.summary'
    cabinetInfo = 'cabinet.info'
    cabinetKeepOrder = 'wxmp.keep.order.create'
    cabinetOrderPaid = 'cabinet.order.paid'
    cabinetOrderOpen = 'cabinet.keep.inbound.open'
    getMobileList = 'wxmp.mobile.list'
    sendSms = 'send.sms'
    addMobile = 'wxmp.add.mobile'
    removeMobile = 'wxmp.remove.mobile'
    orderByCode = 'cabinet.waybill.order.by.code'
    testCheckCode='test.check.code'
    getCheckCode = 'wxmp.get.check.code'
    bannerList = 'banner.list'
    get wxRecharge() { return isAlipay() ? 'alimp.recharge' : 'wxmp.recharge'; }
    openDoor = 'wxmp.access.control.device.open.door'
    billList = 'alimp.waybill.query'
}

const Api = new ApiClass();
export default Api;
