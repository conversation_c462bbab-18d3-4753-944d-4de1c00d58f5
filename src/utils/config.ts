import Config from '../config'

class ConfigClass {
    BrandName = Config.brandName
    CONFIG_COLOR = Config.color  // 主题颜色
    agreement = Config.agreementDomain // 协议域名
    APP_ID = Config.appid // 小程序id
    QQMAP_KEY = 'D6NBZ-VAVKJ-DWTF3-FLWUZ-WZ35K-LHFFU' // webapi map key
    APP_VER = 1
    PAGE_SIZE = 10
    AMAP_KEY = 'f32164fe50561091d606539311aa9087'
    APP_KEY = '6e4db9fa833311e7966812eb78deb327'
    APP_SECRET = '612b051c6e4db9fa833323d515882ccb'
    OSS: any = {
        'region': 'oss-cn-shanghai',
        'endpoint': 'https=//jj-shop-upload.oss-cn-shanghai.aliyuncs.com',
        'accessKeyId': 'LTAISHha05Qbby9q',
        'accessKeySecret': 'UB7O55lVqWf5PtWR0v58sbCQIjJ479',
        'bucketName': 'jj-shop-upload'
    }
    COSa: any = {
        appida: '1301309371', // Bucket 所属的项目 ID
        bucket: 'ctms-tenant-1301309371', // 空间名称 Bucket
        endpoint: 'https=//ctms-upload-1301309371.cos.ap-shanghai.myqcloud.com',
        region: 'ap-shanghai',
        sid: 'AKIDgA4mGdzwjGlwwgsOdgTxPmwHjwteOyFG', // 项目的 SecretID
        skey: 'va0FPYaO1kPrDNmW2LOBMN9ex3tWU1LP' // 项目的 Secret Key
    }
    EXPRESS: any = {
        'ZTO': '中通速递',
        'YTO': '圆通速递',
        'STO': '申通快递',
        'YUNDA': '韵达速递',
        'TTKDEX': '天天快递',
        'HTKY': '百世快递',
        'JT': '极兔速递',
        'SF': '顺丰快递',
        'EMS': 'EMS快递',
        'POSTB': '邮政快递',
        'TM': '天猫快递',
        'JD': '京东快递',
        'SNWL': '苏宁快递'
    }
    ORDER_STATUS: any = {
        1: '包裹待投柜',
        2: '快递员待取出',
        3: '客户已取消',
        4: '快递员已取出',
        5: '快递员拒收',
        6: '快递员取消',
        7: '客户取走包裹',
        9: '包裹已寄出'
    }
    ORDER_REFUND_TYPE: any = {
        1: '客户取消',
        2: '快递员拒绝'
    }
    ORDER_REFUND_STATUS: any = {
        1: '待处理',
        2: '处理中',
        3: '成功',
        4: '失败',
        5: '已拒绝'
    }
    BoxLabel: Map<any, any> = new Map([[0, '极大'],[1, '超大'], [2, '大'], [3, '中'], [4, '小'], [5, '超小'],[6, '极小']])
    AddressType: any = {
        send: '寄件人地址',
        receive: '收件人地址',
        address:'地址管理'
    }
}

const config = new ConfigClass();
export default config;
