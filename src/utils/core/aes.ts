import * as CryptoJ<PERSON> from "crypto-js";

const requestSecret = '0123456qweasdzxc'
const aesSecret = 'qweasdzxc6543210'
const iv = ''

export class AesManager {
    public static encrypt(text: string) {
        const data = text
        const options = {
            iv: CryptoJS.enc.Utf8.parse(iv),
            mode: CryptoJS.mode.ECB,
            // padding: CryptoJS.pad.Pkcs7
        }
        const key = CryptoJS.enc.Utf8.parse(requestSecret)
        const encryptedData = CryptoJS.AES.encrypt(data, key, options)
        const encryptedBase64Str = encryptedData.toString()
        return encryptedBase64Str
    }

    public static decrypt(text: string) {
        const data = text
        const options = {
            mode: CryptoJS.mode.ECB,
            // padding: CryptoJS.pad.Pkcs7
        }
        const key = CryptoJS.enc.Utf8.parse(aesSecret)
        const decryptedData = CryptoJS.AES.decrypt(data, key, options)
        const decryptedStr = decryptedData.toString(CryptoJS.enc.Utf8)
        return decryptedStr
    }

    public static buildSign(data: any, app_secret: string) {
        let str: string = app_secret
        Object.keys(data).sort().forEach((item: string) => {
            str += item + data[item]
        })
        return CryptoJS.MD5(str + app_secret).toString().toLocaleUpperCase()
    }
}
