class CacheClass {
    getItem(key: string) {
        try {
            const value = uni.getStorageSync(key)
            if (value) {
                // Do something with return value
                return value
            }
        } catch (e) {
            // Do something when catch error
        }
    }
    setItem(key: string, value: any) {
        try {
            uni.setStorageSync(key, value)
        } catch (e) {
        }
    }
    removeItem(key: string) {
        try {
            uni.removeStorageSync(key)
        } catch (e) {
            // Do something when catch error
        }
    }
    clear() {
        try {
            uni.clearStorageSync()
        } catch (e) {
            // Do something when catch error
        }
    }
}

const Cache = new CacheClass()
export default Cache