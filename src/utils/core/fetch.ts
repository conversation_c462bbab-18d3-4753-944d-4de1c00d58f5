import Cache from '@/utils/core/cache'
import Modal from '@/utils/core/modal'
import {fetchOptionsDto} from '@/utils/types'
import {AesManager} from "@/utils/core/aes";
// @ts-ignore
import Base64 from './base64'
import Config from "@/config";

const baseApi = Config.baseApi

export function getBaseApi(url: string): string {
    return baseApi + url
    // if (url.startsWith('/track')) {
    //     return 'https://api-gp.zhunpeida.com' + url
    // } else {
    //     return baseApi + url
    // }
}

export function hiddenLoading(showLoading: boolean, time: number, message: string) {
    /**
     * 解决小程序hideLoading 会关闭 toast 的官方bug
     *
     **/
    if (showLoading) {
        const t1 = (new Date()).getTime()
        if (t1 - time < 300) {
            setTimeout(() => {
                uni.hideLoading()
                if (message) {
                    Modal.toast(message)
                }
            }, 300)
        } else {
            uni.hideLoading()
            if (message) {
                Modal.toast(message)
            }
        }
    }
    uni.stopPullDownRefresh()
}

export function Fetch({url, data = {}, method = 'POST', showLoading = false}: fetchOptionsDto) {
    const t = (new Date()).getTime()
    const token = Cache.getItem('SYS_TOKEN')
    method = method.toUpperCase()
    const baseURL = getBaseApi('/api/v1')
    return new Promise<any>((resolve, reject) => {
        let defaultParams: any = {}

        defaultParams['Content-Type'] = 'application/json;charset=UTF-8'
        if (token) {
            defaultParams = {...defaultParams, ...token}
        }

        // defaultParams['App-Sign'] = md5(JSON.stringify(data))

        if (method === 'POST') {
            // if (data['ContentType'] && data['ContentType'].indexOf('json') > -1) {
            // } else {
            //     defaultParams['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
            // }
        }
        const data_string = encodeURIComponent(JSON.stringify(data))

        if (process.env.NODE_ENV === 'development') {
            console.log('请求地址', baseURL + '/' + url)
            console.log('请求参数', data)
        }
        data = {
            'app_key': Config.appKey,
            'data': data_string,
            'name': url,
            'format': 'json',
            'timestamp': new Date().Format('yyyy-MM-dd hh:mm:ss'),
            'version': '1.0'
        }
        const app_secret = Config.secret
        data.sign = AesManager.buildSign(data, app_secret)
        if (showLoading) {
            uni.showLoading({
                title: '加载中...'
            })
        }
        uni.request({
            url: baseURL,
            data: AesManager.encrypt(JSON.stringify(data)),
            method: method,
            header: defaultParams,
            async success(res: any) {
                let message = ''
                if (res.data.code === "0") {
                    const result = AesManager.decrypt(res.data.data)
                    hiddenLoading(showLoading, t, message)
                    let returnRes;
                    try {
                        returnRes = JSON.parse(result)
                    } catch (e) {
                        returnRes = result
                    }
                    if (process.env.NODE_ENV === 'development') {
                        console.log('接口返回', returnRes)
                    }
                    resolve(returnRes)
                } else {
                    if (res.data.code === '18') {
                        if (res.data.msg) {
                            message = res.data.msg
                        }
                        uni.hideLoading()
                        Cache.removeItem('SYS_TOKEN')
                        const pages = getCurrentPages()
                        const currentPage = pages[pages.length - 1]
                        const isLoginPage = currentPage?.route === 'authPages/index/index'
                        if (!isLoginPage) {
                            await Modal.confirm('登录失效，请重新登录', '温馨提示', '暂不', '立即登录')
                            uni.redirectTo({
                                url: '/authPages/index/index'
                            })
                        }                        
                        
                    } else {
                        message = res.data ? res.data.msg : '请求服务失败'
                        hiddenLoading(showLoading, t, message)
                        reject(res)
                    }
                }
            },
            fail(e) {
                hiddenLoading(showLoading, t, '服务器出小差啦~')
                reject(e)
            },
            complete() {
                // if (showLoading) {
                //     const t1 = (new Date()).getTime()
                //     if (t1 - t < 1000) {
                //         setTimeout(() => {
                //             uni.hideLoading()
                //         }, 1000)
                //     } else {
                //         uni.hideLoading()
                //     }
                // }
            }
        })
    })
}
