import config from "@/utils/config";
import Vue from "vue";

class ModalClass {
    alert(msg: string, title: string = '温馨提示') {
        return new Promise((resolve, reject) => {
            uni.showModal({
                title: title,
                content: msg,
                showCancel: false,
                confirmColor: '#51A830',
                success(res) {
                    if (res.confirm) {
                        resolve(res.confirm)
                    } else {
                        reject()
                    }
                }
            })
        })
    }

    confirm(msg: string, title: string = '温馨提示', cancelText: string = '取消', confirmText: string = '确定') {
        let color = config.CONFIG_COLOR
        return new Promise((resolve, reject) => {
            uni.showModal({
                title: title,
                content: msg,
                showCancel: true,
                cancelText: cancelText,
                confirmText: confirmText,
                confirmColor: color,
                cancelColor: '#333333',
                success(res) {
                    if (res.confirm) {
                        resolve(res.confirm)
                    } else {
                        reject()
                    }
                }
            })
        })
    }

    confirmAllBack(msg: string, title: string, {
        confirmText = '确定',
        confirmColor = '#51A830',
        cancelText = '取消',
        cancelColor = '#ababab'
    }) {
        return new Promise((resolve, reject) => {
            uni.showModal({
                title: title || '温馨提示',
                content: msg,
                showCancel: true,
                confirmText,
                confirmColor,
                cancelText,
                cancelColor,
                success(res) {
                    if (res.confirm) {
                        resolve(true)
                    } else {
                        resolve(false)
                    }
                }
            })
        })
    }

    confirmPay(msg: string, title: string) {
        return new Promise((resolve, reject) => {
            uni.showModal({
                title: title || '温馨提示',
                content: msg,
                showCancel: true,
                confirmColor: '#51A830',
                cancelColor: '#ababab',
                success(res) {
                    if (res.confirm) {
                        resolve(true)
                    } else {
                        resolve(false)
                    }
                }
            })
        })
    }

    toast(message: string, time = 2000) {
        let msg = message
        if (typeof msg === 'object') {
            msg = JSON.stringify(message)
        }
        uni.showToast({
            title: msg,
            icon: 'none',
            mask: false,
            image: '',
            duration: time || 2000
        })
    }

    showErrorToast(msg: string) {
        uni.showToast({
            title: msg,
            image: 'https://img.365ptd.com/static/images/icon_error.png'
        })
    }

    showActionSheet(itemList: any[]) {
        return new Promise((resolve, reject) => {
            uni.showActionSheet({
                itemList: itemList,
                // itemColor: config.PRIMARY_COLOR,
                success(res) {
                    resolve(res.tapIndex)
                },
                fail() {
                    reject()
                }
            })
        })
    }

    // 自定义弹窗
    confirmCustom(msg: string, title: string = '温馨提示', cancelText: string = '取消', confirmText: string = '确定', showCancel: boolean = true) {
        let color = config.CONFIG_COLOR
        const vm = new Vue()
        return new Promise((resolve, reject) => {
            // @ts-ignore
            vm.$showModal({
                title: title,
                content: msg,
                cancelText: cancelText,
                confirmText: confirmText,
                confirmColor: color,
                showCancel: showCancel,
                success(res: any) {
                    if (res.confirm) {
                        console.log('用户点击确定')
                        resolve(res.confirm)
                    } else if (res.cancel) {
                        reject()
                        console.log('用户点击取消')
                    }
                }
            })
        })
    }
}

const Modal = new ModalClass()
export default Modal