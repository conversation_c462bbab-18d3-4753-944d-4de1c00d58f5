import { C<PERSON>, Modal } from "@/utils/index";
import { getUserInfo, login, wempBindPhone } from "@/model/login";
import Config from "@/utils/config";
import { longLatDto, userDto, wechatPhoneDto } from "@/utils/types";


// 获取平台类型
function getPlatform() {
    return uni.getSystemInfoSync().app;
  }
  
  // 判断是否为支付宝小程序
export function isAlipay() {
    return getPlatform() === "alipay";
  }

export function isWechat() {
    return getPlatform() === "weixin";
  }

export function getLoginCode(): Promise<string> {
  return new Promise((resolve, reject) => {
    if (isAlipay()) {
      // 支付宝小程序登录（必须使用 my.getAuthCode）
      my.getAuthCode({
        scopes: "auth_user", // 可选 auth_base（静默授权）或 auth_user（主动授权）
        success: (res) => {
          console.log("支付宝登录成功", res.authCode);
          resolve(res.authCode);
          // 将 authCode 发送到后端换取用户信息
        },
        fail: () => {
            reject("登录失败");
        //   console.error("支付宝登录失败", err);
        },
      });
    } else {
      uni.login({
        provider: "weixin",
        success: (res) => {
          resolve(res.code);
        },
        fail: (err) => {
          // @ts-ignore
          reject("登录失败");
        },
      });
    }
  });
}
export function getGzhOpenId(): Promise<string> {
  return Cache.getItem("gzhOpenId");
}
export function getGzhAppId(): Promise<string> {
  return Cache.getItem("gzhAppId");
}

export function getWxUserInfo(flag = false) {
    return new Promise((resolve) => {
      if (isAlipay()) {
        console.log("支付宝获取用户信息");
        // 支付宝获取用户信息
        my.getAuthUserInfo({
          success: (res) => {
            console.log("支付宝获取用户信息成功", res);
            resolve(res);
          },
          fail: () => {
            resolve(false);
          },
        });
      } else {
        // 微信获取用户信息
        let canIUseGetUserProfile: boolean = false;
        // @ts-ignore
        if (uni.getUserProfile) {
          canIUseGetUserProfile = flag;
        }
        
        if (canIUseGetUserProfile) {
          // @ts-ignore
          uni.getUserProfile({
            provider: "weixin",
            desc: "获取你的头像、昵称",
            success: (result: any) => {
              resolve(result);
            },
            fail: () => {
              resolve(false);
            },
          });
        } else {
          uni.getUserInfo({
            provider: "weixin",
            success: (result) => {
              resolve(result);
            },
            fail: () => {
              resolve(false);
            },
          });
        }
      }
    });
  }
  

// 微信登录
// export function loginByWechat() {
//     return new Promise<userDto | boolean>((resolve, reject) => {
//         uni.login({
//             provider: 'weixin',
//             success: async function (res) {
//                 const code = res.code
//                 uni.getUserProfile({
//                     desc: '获取你的头像、昵称',
//                     success: function (result) {
//                         // const info: loginDto = {
//                         //     appId: Config.APP_ID,
//                         //     code,
//                         //     encryptedData: result.encryptedData,
//                         //     iv: result.iv,
//                         //     rawData: result.rawData,
//                         //     signature: result.signature
//                         // }
//                         // login(info).then((data) => {
//                         //     let a = {}
//                         //     // @ts-ignore
//                         //     a[data.tokenName] = data.tokenValue
//                         //     Cache.setItem('SYS_TOKEN', a)
//                         //     const time = new Date().getTime()
//                         //     Cache.setItem('last_login_time', time)
//                         //     resolve(data)
//                         // })
//                     },
//                     fail: function (err) {
//                         console.log(err);
//                         resolve(false)
//                     }
//                 })
//             },
//             fail: function (err) {
//                 console.log(err);
//             }
//         })
//     })
// }
// 微信登录
export async function loginByWechat() {
  const _info: any = await getWxUserInfo(true);
  if (!_info) {
      return false
  }
  const code: string | boolean = await getLoginCode();
  if (!code) {
    return false;
  }
  const gzhOpenId: string | undefined = await getGzhOpenId();
  if (!gzhOpenId) {
    Cache.removeItem("gzhOpenId");
  }
  const gzhAppId: string | undefined = await getGzhAppId();
  if (!gzhAppId) {
    Cache.removeItem("gzhAppId");
  }
  return new Promise<userDto | boolean>(async (resolve, reject) => {
    const info: any = {
      appId: Config.APP_ID,
      code: code,
      // avatarUrl: _info.userInfo.avatarUrl,
      // nickName: _info.userInfo.nickName,
      // language: _info.userInfo.language,
      // province: _info.userInfo.province,
      // city: _info.userInfo.city,
      // country: _info.userInfo.country,
      // gender: _info.userInfo.gender,
      // gzhOpenId: gzhOpenId,
      // gzhAppId: gzhAppId avatar
      avatarUrl: isAlipay() ? _info ? _info.avatar :"" : _info ? _info.avatar :"",
      nickName: isAlipay() ? _info ? _info.nickName :"支付宝用户" : _info ? _info.nickName :"微信用户",
      language: "",
      province: "",
      city: "",
      country: "",
      gender: "",
      gzhOpenId: gzhOpenId,
      gzhAppId: gzhAppId,
    };
    console.log("info", info);
    login(info)
      .then((data) => {
        let a = {};
        // @ts-ignore
        a[data.tokenName] = data.tokenValue;
        Cache.setItem("SYS_TOKEN", a);
        const time = new Date().getTime();
        Cache.setItem("last_login_time", time);
        resolve(data);
      })
      .catch((e) => {
        resolve(false);
      });
  });
}

export function checkIsAuth() {
  const token = Cache.getItem("SYS_TOKEN");
  return token != null;
}

export function checkExpireTime(): boolean {
  let expireTime = Cache.getItem("expireTime");
  if (expireTime !== undefined) {
    console.log(
      expireTime,
      new Date().getTime(),
      expireTime * 1 - new Date().getTime() <= 0
    );
    return expireTime * 1 - new Date().getTime() <= 0;
  }
  return false;
}

export function removeOuter() {
  Cache.removeItem("expireTime");
  Cache.removeItem("orderType");
  Cache.removeItem("scanType");
  Cache.removeItem("cabinetLocationCode");
  Cache.removeItem("cabinetLocationId");
}

export function checkUser() {
  const user = Cache.getItem("user");
  if (user) {
    return user.phone != null && user.phone != "";
  }
  return false;
}

// 判断登录状态(是否登录、是否过期)
export function checkLogin() {
  const token = Cache.getItem("SYS_TOKEN");
  if (token) {
    const lastTime = Cache.getItem("last_login_time");
    const now = new Date().getTime();
    if (now - lastTime > 60 * 60 * 1000) {
      return loginByWechat();
    } else {
      return Cache.getItem("userMobile");
    }
  } else {
    return loginByWechat();
  }
}

// 同步本地user缓存
export async function syncUser() {
  const user: any = await getUserInfo();
  Cache.setItem("user", user);
}

export function LogOut() {
  Modal.confirm("小主~，确认清除本地缓存嘛?").then(() => {
    Cache.clear();
    uni.reLaunch({
      url: "/pages/main/index",
    });
  });
}

export function bindPhone(data: wechatPhoneDto) {
    return new Promise<any>((resolve, reject) => {
      if (isAlipay()) {
        // 支付宝获取手机号
        my.getPhoneNumber({
          success: (res) => {
            console.log("支付宝获取手机号res", res.response);
            const infoAlipay = {
              appId: Config.APP_ID,
              response: res.response,
              platform: "alipay",
            };
            wempBindPhone(infoAlipay).then((res) => {
              resolve(res);
            });
          },
          fail: (err) => {
            resolve(false);
          },
        });
      } else {
        // 微信获取手机号
        wx.login({
          success: (res) => {
            const code = res.code;
            const info = {
              appId: Config.APP_ID,
              encryptedData: data.detail.encryptedData,
              code,
              iv: data.detail.iv,
              platform: "weixin",
            };
            wempBindPhone(info).then((res) => {
              resolve(res);
            });
          },
          fail: (err) => {
            resolve(false);
          },
        });
      }
    });
  }

// 微信授权绑定手机号
// export function bindWechatPhone(data: wechatPhoneDto, isGoHome: boolean = true) {
//     uni.login({
//         provider: 'weixin',
//         success: function (res) {
//             const code = res.code
//             const info = {
//                 appId: Config.APP_ID,
//                 encryptedData: data.detail.encryptedData,
//                 code,
//                 iv: data.detail.iv
//             }
//             wempBindPhone(info).then((res) => {
//                 console.log('res', res);
//                 if (isGoHome) {
//                     uni.reLaunch({
//                         url: '/pages/home/<USER>/index'
//                     })
//                 }
//             }).catch(e => {
//                 Modal.toast('授权手机号失败,请稍后重试')
//             })
//         }
//     })
// }

export function getSetting(authName: string, message: string) {
  return new Promise((resolve) => {
    uni.getSetting({
      success: (res) => {
        // @ts-ignore
        if (res.authSetting[authName] === false) {
          Modal.alert(message).then(() => {
            uni.openSetting({
              success: (res) => {
                resolve(res);
              },
            });
          });
        } else {
          resolve(res);
        }
      },
    });
  });
}

export function getLocation(): Promise<longLatDto> {
  return new Promise((resolve) => {
    getSetting("scope.userLocation", "请授权获取地址").then(() => {
      uni.getLocation({
        type: "wgs84",
        success: function (res) {
          resolve({ longitude: res.longitude, latitude: res.latitude });
        },
        fail: function () {
          getLocation();
        },
      });
    });
  });
}

export function getCityCode() {
  return new Promise((resolve) => {
    getSetting("scope.userLocation", "请授权获取地址").then(() => {
      uni.getLocation({
        type: "wgs84",
        success: function (res) {
          uni.request({
            url: "https://apis.map.qq.com/ws/geocoder/v1",
            data: {
              key: "BD7BZ-FQ46X-FBQ4F-T3GV4-WIXRE-5KBTC",
              location: res.latitude + "," + res.longitude,
              get_poi: 1,
              output: "json",
            },
            method: "GET",
            success: function (res: any) {
              if (
                res.data &&
                res.data.result &&
                res.data.result.ad_info &&
                res.data.result.ad_info.city_code &&
                res.data.result.ad_info.nation_code
              ) {
                const city_code = res.data.result.ad_info.city_code;
                const nation_code = res.data.result.ad_info.nation_code;
                const code = city_code.replace(nation_code, "");
                console.log("res.data", res.data);
                resolve(code);
              } else {
                resolve(null);
              }
            },
          });
        },
        fail: function (err) {
          console.log(err);
        },
      });
    });
  });
}

// 封装支付宝定位权限脚本

export const authGuideLocation = async () => {
  const myGetSystemInfo = () => {
    return new Promise((resolve, reject) => {
      my.getSystemInfo({
        success: resolve,
        fail: reject
      });
    });
  };
  const myGetSetting = () => {
    return new Promise((resolve, reject) => {
      my.getSetting({
        success: resolve,
        fail: reject
      });
    });
  };
  const myOpenSetting = () => {
    return new Promise((resolve, reject) => {
      my.openSetting({
        success: resolve,
        fail: reject
      });
    });
  };

  // 获取用户是否开启系统定位及授权支付宝使用定位
  const isLocationEnabled = async () => {
    const systemInfo = await myGetSystemInfo();
    return !! ((systemInfo as any).locationEnabled && (systemInfo as any).locationAuthorized);
  };
  // 若用户未开启系统定位或未授权支付宝使用定位，则跳转至系统设置页
  const showAuthGuideIfNeeded = async () => {
    if (!(await isLocationEnabled())) {
      my.showAuthGuide({
        authType: "LBS"
      });
      return false;
    }
    return true;
  };
  // 获取用户是否授权过当前小程序使用定位
  const isLocationMPAuthorized = async () => {
    const settingInfo = await myGetSetting();
    return (settingInfo as any).authSetting.location === undefined || (settingInfo as any).authSetting.location;
  };
  // 若用户未授权当前小程序使用定位，则引导用户跳转至小程序设置页开启定位权限
  const requestLocationPermission = async () => {
    await Modal.alert("您之前取消过授权，是否前往授权？");
    const openSettingInfo = await myOpenSetting();
    return (openSettingInfo as any).authSetting.location;
  };
  try {
    if (!(await showAuthGuideIfNeeded())) {
      return false;
    }
    if (await isLocationMPAuthorized()) {
      return true;
    }
    if (await requestLocationPermission()) {
      return true;
    }
    return false;
  } catch (error) {
    console.error(error);
    return false;
  }
};