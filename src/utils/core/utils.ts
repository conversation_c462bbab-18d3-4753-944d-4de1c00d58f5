import logo from '@/static/img/home/<USER>/logo.png'
import logoConfig from '@/static/imgConfig/logoConfig.png'
import friendCenter from '@/myPages/static/friendCenter.png'
import keep from '@/static/img/home/<USER>/keep.png'
import friends from '@/static/img/home/<USER>/friends.png'
import apply from '@/static/img/home/<USER>/apply.png'
import address from '@/static/img/home/<USER>/address.png'
import more from '@/static/img/home/<USER>/more.png'
import qj from '@/static/img/home/<USER>/qj.png'
import qjConfig from '@/static/imgConfig/qjConfig.png'
import jj from '@/static/img/home/<USER>/jj.png'
import jjConfig from '@/static/imgConfig/jjConfig.png'
import wdkd from '@/static/img/home/<USER>/wdkd.png'
import wdkdConfig from '@/static/imgConfig/wdkdConfig.png'
import wjqjm from '@/static/img/home/<USER>/wjqjm.png'
import wjqjmConfig from '@/static/imgConfig/wjqjmConfig.png'
import box from '@/static/img/home/<USER>/box.png'
import payOnline from '@/homePages/static/payOnline.png'
import receive from '@/static/img/home/<USER>/receive.png'
import send from '@/static/img/home/<USER>/send.png'
import book from '@/static/img/home/<USER>/book.png'
import location from '@/homePages/static/location.png'
import scan from '@/static/img/home/<USER>/scan.png'
import recent from '@/static/img/home/<USER>/recent.png'
import fjfwd from '@/static/img/home/<USER>/fjfwd.png'
import fjfwdConfig from '@/static/imgConfig/fjfwdConfig.png'
import yzjj from '@/static/img/home/<USER>/yzjj.png'
import yzjjConfig from '@/static/imgConfig/yzjjConfig.png'
import zcdd from '@/static/img/home/<USER>/zcdd.png'
import zcddConfig from '@/static/imgConfig/zcddConfig.png'
import ZTO from '@/static/img/home/<USER>/zt.png'
import STO from '@/static/img/home/<USER>/st.png'
import YTO from '@/static/img/home/<USER>/yt.png'
import YUNDA from '@/static/img/home/<USER>/yd.png'
import TTKDEX from '@/static/img/home/<USER>/tt.png'
import HTKY from '@/static/img/home/<USER>/bs.png'
import EMS from '@/static/img/home/<USER>/ems.png'
import SF from '@/static/img/home/<USER>/sf.png'
import POSTB from '@/static/img/home/<USER>/yz.png'
import TM from '@/static/img/home/<USER>/tm.png'
import JD from '@/static/img/home/<USER>/jd.png'
import SNWL from '@/static/img/home/<USER>/sn.png'
import JT from '@/static/img/home/<USER>/jt.png'
import select from '@/static/img/home/<USER>/select.png'
import selectConfig from '@/static/img/home/<USER>/selectConfig.png'
import unselect from '@/static/img/home/<USER>/unselect.png'
import cabinet from '@/homePages/static/cabinet.png'
import cw from '@/homePages/static/cw.png'
import openDoor from '@/static/img/home/<USER>/openDoor.jpg'
import openDoorConfig from '@/static/imgConfig/openDoorConfig.jpg'

const imgUrl = {
    logo: logo,
    friendCenter: friendCenter,
    keep: keep,
    friends: friends,
    apply: apply,
    more: more,
    address: address,
    qj: qj,
    jj: jj,
    wjqjm: wjqjm,
    wdkd: wdkd,
    box: box,
    payOnline: payOnline,
    receive: receive,
    send: send,
    book: book,
    location: location,
    scan: scan,
    recent: recent,
    fjfwd: fjfwd,
    yzjj: yzjj,
    zcdd: zcdd,
    ZTO: ZTO,
    STO: STO,
    YTO: YTO,
    YUNDA: YUNDA,
    TTKDEX: TTKDEX,
    HTKY: HTKY,
    EMS: EMS,
    SF: SF,
    POSTB: POSTB,
    TM: TM,
    JD: JD,
    SNWL: SNWL,
    JT: JT,
    select: select,
    unselect: unselect,
    cabinet: cabinet,
    CW: cw,
    openDoor: openDoor,
}
if (process.env.VUE_APP_CHANNEL === 'yzgp') {
    imgUrl.logo = logoConfig
    imgUrl.qj = qjConfig
    imgUrl.jj = jjConfig
    imgUrl.fjfwd = fjfwdConfig
    imgUrl.yzjj = yzjjConfig
    imgUrl.zcdd = zcddConfig
    imgUrl.select = selectConfig
    imgUrl.wdkd = wdkdConfig
    imgUrl.wjqjm = wjqjmConfig
    imgUrl.openDoor = openDoorConfig
}

class UtilsClass {
    getImage(type: string) {
        // @ts-ignore
        return imgUrl[type]
    }

    getCompanyLogo(brandCode: string) {
        let img = ''
        switch (brandCode) {
            case 'ZTO':
                img = this.getImage('ZTO')
                break
            case 'STO':
                img = this.getImage('STO')
                break
            case 'YTO':
                img = this.getImage('YTO')
                break
            case 'YUNDA':
                img = this.getImage('YUNDA')
                break
            case 'TTKDEX':
                img = this.getImage('TTKDEX')
                break
            case 'HTKY':
                img = this.getImage('HTKY')
                break
            case 'EMS':
                img = this.getImage('EMS')
                break
            case 'SF':
                img = this.getImage('SF')
                break
            case 'POSTB':
                img = this.getImage('POSTB')
                break
            case 'TM':
                img = this.getImage('TM')
                break
            case 'JD':
                img = this.getImage('JD')
                break
            case 'SNWL':
                img = this.getImage('SNWL')
                break
            case 'JT':
                img = this.getImage('JT')
                break
            case 'CW':
                img = this.getImage('CW')
                break
        }
        return img
    }

    hidePhone(phone: string): string {
        return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }

    setDistance(distance: number) {
        if (!distance) {
            return 0
        }
        return (distance / 1000).toFixed(1)
    }

    isDuringTime(beginTime: string, endTime: string, nowTime: string) {
        const strb = beginTime.split(":");
        if (strb.length != 2) {
            return false;
        }

        const stre = endTime.split(":");
        if (stre.length != 2) {
            return false;
        }

        const strn = nowTime.split(":");
        if (stre.length != 2) {
            return false;
        }
        const b = new Date();
        const e = new Date();
        const n = new Date();

        b.setHours(Number(strb[0]));
        b.setMinutes(Number(strb[1]));
        e.setHours(Number(stre[0]));
        e.setMinutes(Number(stre[1]));
        n.setHours(Number(strn[0]));
        n.setMinutes(Number(strn[1]));

        return n.getTime() - b.getTime() > 0 && n.getTime() - e.getTime() < 0;
    }
}

const Utils = new UtilsClass()
export default Utils
