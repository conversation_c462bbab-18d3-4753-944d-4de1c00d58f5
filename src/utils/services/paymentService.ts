import { isAlipay } from '@/utils/core/uni';
import { createPayOrder } from '@/model/send';
import {getPayParams} from "@/model/pay";
import { wxRecharge } from '@/model/cabinet';
import Modal from '@/utils/core/modal';
import { Cache } from '@/utils';
import Config from '@/utils/config';

// 支付参数接口
export interface PaymentParams {
  orderId?: string;
  cabinetLocationCode?: string;
  hostIndex?: string;
  userId?: string;
  money?: number;
  [key: string]: any; // 允许其他参数
}

// 支付结果接口
export interface PaymentResult {
  success: boolean;
  tradeNo?: string;
  totalFee?: number;
  message?: string;
  data?: any;
}

class PaymentService {
  /**
   * 统一支付方法
   * @param params 支付参数
   * @param payType 支付类型：'order'(订单支付) 或 'recharge'(充值支付)
   */
  async pay(params: PaymentParams, payType: 'order' | 'recharge' = 'order'): Promise<PaymentResult> {
    if (isAlipay()) {
      return this.alipayPayment(params, payType);
    } else {
      return this.wechatPayment(params, payType);
    }
  }

  /**
   * 微信支付
   */
  private async wechatPayment(params: PaymentParams, payType: 'order' | 'recharge'): Promise<PaymentResult> {
    try {
      if (payType === 'order') {
        // 订单支付
        const payParams = {
          cabinetLocationCode: String(params.cabinetLocationCode) ,
          orderId: String(params.orderId) ,
          hostIndex: params.hostIndex || String(Cache.getItem("hostIndex") || ""),
        };
        
        const res = await getPayParams(payParams);
        if (!res) {
          return {
            success: true,
            tradeNo: "",
            totalFee: 0,
          };
        }
        
        return new Promise((resolve) => {
          wx.requestPayment({
            timeStamp: res.timeStamp,
            nonceStr: res.nonceStr,
            package: res.packageValue,
            signType: res.signType,
            paySign: res.paySign,
            success: function() {
              resolve({
                success: true,
                tradeNo: res.tradeNo,
                totalFee: res.totalFee,
                data: res
              });
            },
            fail: function(err) {
              console.error("支付失败:", err);
              resolve({
                success: false,
                message: "支付失败，请重试"
              });
            }
          });
        });
      } else {
        // 充值支付
        return new Promise((resolve) => {
          wx.login({
            success: function(res) {
              const code = res.code;
              const rechargeParams = {
                userId: params.userId,
                code: code,
                money: params.money,
              };
              
              wxRecharge(rechargeParams).then((result) => {
                wx.requestPayment({
                  timeStamp: result.timestamp,
                  nonceStr: result.noncestr,
                  package: result.packageValue,
                  signType: result.signType,
                  paySign: result.sign,
                  success: function() {
                    resolve({
                      success: true,
                      data: result
                    });
                  },
                  fail: function(err) {
                    console.error("充值失败:", err);
                    resolve({
                      success: false,
                      message: "充值失败，请重试"
                    });
                  }
                });
              }).catch(err => {
                resolve({
                  success: false,
                  message: "创建充值订单失败"
                });
              });
            },
            fail: function() {
              resolve({
                success: false,
                message: "获取登录凭证失败"
              });
            }
          });
        });
      }
    } catch (error) {
      console.error(payType === 'order' ? "创建支付订单失败:" : "创建充值订单失败:", error);
      return {
        success: false,
        message: payType === 'order' ? "创建支付订单失败，请重试" : "创建充值订单失败，请重试"
      };
    }
  }

  /**
   * 支付宝支付
   */
  private async alipayPayment(params: PaymentParams, payType: 'order' | 'recharge'): Promise<PaymentResult> {
    try {
      if (payType === 'order') {
        // 订单支付
        const payParams = {
          cabinetLocationCode: params.cabinetLocationCode,
          orderId: params.orderId,
          hostIndex: params.hostIndex || Cache.getItem("hostIndex"),
          platform: "alipay"
        };
        
        // 这里需要后端提供支付宝支付的API
        const res = await this.createAlipayOrder(payParams);
        if (!res) {
          return {
            success: true,
            tradeNo: "",
            totalFee: 0,
          };
        }
        
        return new Promise((resolve) => {
          my.tradePay({
            tradeNO: res.tradeNo,
            success: function(result) {
              if (result.resultCode === '9000') {
                resolve({
                  success: true,
                  tradeNo: res.tradeNo,
                  totalFee: res.totalFee,
                  data: res
                });
              } else {
                console.error("支付宝支付失败:", result);
                resolve({
                  success: false,
                  message: "支付失败，请重试"
                });
              }
            },
            fail: () => {
              console.error("支付宝支付失败:");
              resolve({
                success: false,
                message: "支付失败，请重试"
              });
            }
          });
        });
      } else {
        // 充值支付
        return new Promise((resolve) => {
          my.getAuthCode({
            scopes: 'auth_base',
            success: function(res) {
              const authCode = res.authCode;
              const rechargeParams = {
                userId: params.userId,
                authCode: authCode,
                money: params.money,
                platform: "alipay"
              };
              
              // 这里需要后端提供支付宝充值的API
              PaymentService.prototype.alipayRecharge(rechargeParams).then((result) => {
                my.tradePay({
                  tradeNO: result.tradeNo,
                  success: function(res) {
                    if (res.resultCode === '9000') {
                      resolve({
                        success: true,
                        data: result
                      });
                    } else {
                      console.error("支付宝充值失败:", res);
                      resolve({
                        success: false,
                        message: "充值失败，请重试"
                      });
                    }
                  },
                  fail: function() {
                    console.error("支付宝充值失败:");
                    resolve({
                      success: false,
                      message: "充值失败，请重试"
                    });
                  }
                });
              }).catch(err => {
                resolve({
                  success: false,
                  message: "创建充值订单失败"
                });
              });
            },
            fail: function() {
              resolve({
                success: false,
                message: "获取授权码失败"
              });
            }
          });
        });
      }
    } catch (error) {
      console.error(payType === 'order' ? "创建支付宝支付订单失败:" : "创建支付宝充值订单失败:", error);
      return {
        success: false,
        message: payType === 'order' ? "创建支付宝支付订单失败，请重试" : "创建支付宝充值订单失败，请重试"
      };
    }
  }

  /**
   * 创建支付宝支付订单（需要后端支持）
   */
  private async createAlipayOrder(data: any) {
    // 示例： return Fetch({url: Api.payParams, data: info, method: 'POST', showLoading: true})
    const userInfo = Cache.getItem("user")
    const payParams = {
            ...data,
            openId: userInfo?.openId || '', // 支付宝用户的openId
            appId: Config.APP_ID || '',   // 支付宝小程序appId
        };
    return getPayParams(payParams);
  }

  /**
   * 支付宝充值（需要后端支持）
   */
  private async alipayRecharge(data: any) {
    // 这里需要后端提供支付宝充值的API
    // 示例：return Fetch({url: Api.alipayRecharge, data: data, method: 'POST', showLoading: true});
    // 暂时使用与微信相同的API，后端需要根据platform参数区分处理
    return wxRecharge(data);
  }

  /**
   * 处理支付结果，跳转到支付结果页面
   */
  handleOrderPaymentResult(result: PaymentResult, params: PaymentParams,type: string) {
    if (result.success) {
      let info = {
        orderId: params.orderId,
        tradeNo: result.tradeNo || "",
        totalFee: result.totalFee || 0,
        cabinetLocationCode: params.cabinetLocationCode,
        checkCode: params.checkCode,
        type: type,
      };
      uni.navigateTo({
        url: "/homePages/storageOrder/storageOrderStatus/storageOrderStatus?info=" + JSON.stringify(info),
      });
    } else {
      Modal.toast(result.message || "支付失败，请重试");
    }
  }

  /**
   * 处理充值结果
   */
  handleRechargeResult(result: PaymentResult, callback?: (success: boolean) => void) {
    if (callback) {
      callback(result.success);
    } else if (!result.success) {
      Modal.toast(result.message || "充值失败，请重试");
    }
  }
}

export default new PaymentService();
