// 运单信息
export enum InputTypeDto {
    INPUT = 'input',
    SELECT = 'select',
    SWITCH = 'switch'
}

export interface addressDto {
    name: string;
    text: string;
    mobile: string;
    address: string;
    provinceCode: string;
    provinceName: string;
    cityCode: string;
    cityName: string;
    areaCode: string;
    areaName: string;
    pca: string;
    streetCode: string;
    streetName: string;
    hasDefault: number;
    company: string;
}

export class addressInfo implements addressDto {
    name: string = '';
    mobile: string = '';
    text: string = '';
    address: string = '';
    provinceCode: string = '';
    provinceName: string = '';
    pca: string = '';
    cityCode: string = '';
    cityName: string = '';
    areaCode: string = '';
    areaName: string = '';
    streetCode: string = '';
    streetName: string = '';
    hasDefault: number = 0;
    company: string = '';
}

export interface waybillDto {
    id: string;
    orderWaybillId: string;
    orderId: string;
    waybillNo: string;
    cabinetName: string;
    cabinetLocationName: string;
    cabinetLocationCode: string;
    brandCode: string;
    brandName: string;
    checkCode: string;
    receiverMobile: string;
    inboundTime: string;
    receiverName: string;
    keepTime: string;
    price: number;
    predictPrice: number;
    outboundUserFee: number;
    pickStatus: number;
    feeStatus: number;
    hasOutbound: number;
    cabinetBoxLabel: string;
    cabinetBoxType: number;
    inboundYmd: string;
}


export class waybillInfo implements waybillDto {
    id: string = '';
    orderWaybillId: string = '';
    orderId: string = '';
    waybillNo: string = '';
    shopName: string = '';
    cabinetName: string = '';
    cabinetLocationName: string = '';
    cabinetLocationCode: string = '';
    brandCode: string = '';
    brandName: string = '';
    checkCode: string = '';
    keepTime: string = '';
    inboundTime: string = '';
    cabinetBoxLabel: string = '';
    price: number = 0;
    storeType: number = 0;
    predictPrice: number = 0;
    outboundUserFee: number = 0;
    hasOutbound: number = 0;
    pickStatus: number = 0;
    feeStatus: number = 0;
    cabinetBoxType: number = 3;
    receiverName: string = '';
    receiverMobile: string = '';
    inboundYmd: string = '';
}

// 门店信息
export interface shopInfoDto {
    id?: string;
    distance?: number;
    name: string;
    code: string;
    siteName: string;
    siteCode: string;
    address: string;
    businessHours: string;
    latitude: number;
    longitude: number;
    cityName?: string;
    cityCode?: string;
    countyCode?: string;
    countyName?: string;
    provinceCode?: string;
    provinceName?: string;
    mobile: string;
}

export class shop implements shopInfoDto {
    id?: string;
    name: string = '';
    code: string = '';
    siteName: string = '';
    siteCode: string = '';
    distance?: number;
    address: string = '';
    businessHours: string = '';
    latitude: number = 0;
    longitude: number = 0;
    cityName?: string = '';
    cityCode?: string = '';
    countyCode?: string = '';
    countyName?: string = '';
    provinceCode?: string = '';
    provinceName?: string = '';
    mobile: string = '';
}

// 绑定手机号参数
export interface bindPhoneDto {
    appId: string;
    encryptedData: string;
    code: string;
    iv: string;
}

// 支付宝绑定手机号参数
export interface bindPhoneZfDto {
    appId: string;
    response: string;
}

// 实名认证参数
export interface realNameDto {
    realName: string;
    idNumber: string;
}


export class location implements locationDto {
    latitude: number = 0;
    longitude: number = 0;
}

// 经纬度
export interface locationDto {
    longitude: number;
    latitude: number;
}

export interface cabinetCodeDto {
    cabinetId?: string;
    cabinetLocationCode?: string;
    orderType?: number;
    isMobileLast4?:boolean;
}

export interface outboundDto {
    cabinetLocationCode: string;
    orderId: string;
}

export interface payDto {
    orderId: string;
    cabinetLocationCode: string;
    hostIndex?:string;
    openId?: string;  // 支付宝支付需要
    appId?: string;   // 支付宝支付需要
}

export interface cabinetDto {
    code: string;
    name: string;
    type: number;
    linkman: string;
    mobile: string;
    cabinetType: number;
    provinceCode: string;
    provinceName: string;
    cityCode: string;
    cityName: string;
    areaCode: string;
    areaName: string;
    address: string;
    longitude: string;
    latitude: string;
    servicePhone: string;
    distance: number;
    hugeCount: number;
    largeCount: number;
    mediumCount: number;
    smallCount: number;
    sendJson: string;
    switchSend: number;
    miniCount: number;
}

export class cabinetForm implements cabinetDto {
    code: string = '';
    name: string = '';
    type: number = 0;
    sendJson: string = '{}';
    linkman: string = '';
    mobile: string = '';
    cabinetType: number = 0;
    provinceCode: string = '';
    provinceName: string = '';
    cityCode: string = '';
    cityName: string = '';
    areaCode: string = '';
    areaName: string = '';
    address: string = '';
    longitude: string = '';
    latitude: string = '';
    servicePhone: string = '';
    distance: number = 0;
    superCount: number = 0;
    hugeCount: number = 0;
    largeCount: number = 0;
    mediumCount: number = 0;
    smallCount: number = 0;
    miniCount: number = 0;
    microCount: number = 0;
    switchSend: number = 1;
}

// 登录参数
export interface loginDto {
    code: string | boolean;
    appId: string;
    avatarUrl: string;
    nickName: string;
    language: string;
    province: string;
    city: string;
    country: string;
    gender: number;
    gzhAppId?: string;
    gzhOpenId?: string;
}

// 用户信息
export interface userDto {
    id: string;
    avatar?: string;
    nickName?: string;
    phone: string;
    tokenName?: string;
    tokenValue?: string;
    isLogin?: boolean;
}

// 网络请求
export interface fetchOptionsDto {
    url: string;
    data: any;
    method: any;
    showLoading: boolean
}

// 微信手机号
export interface wechatPhoneDto {
    detail: phoneMd5Dto;
    code: string;
}

// 微信手机号加密信息
export interface phoneMd5Dto {
    iv: string;
    encryptedData: string;
    errMsg: string;
}

// 查询待取件
export interface inboundQueryDto {
    mobile: string;
    shopCode: string;
    siteCode: string;
}

// 扫码跳转小程序传递参数
export interface outerDto {
    q?: string;
    i?: string;
    cabinetLocationCode?: string;
    expireTime?: number;
    orderType?: number;
    path?: string;
    scene?: string;
    gzhAppId?: string;
    gzhOpenId?: string;
}

// 经纬度
export interface longLatDto {
    longitude: number;
    latitude: number;
    switchSend?: number;
}

// 分页查询参数
export interface pageDto {
    current: number;
    size: number;
    orderBy: string;
    orderDir: string;
}

export class pickUpSearchDto implements pageDto {
    current: number = 1;
    size: number = 10;
    orderBy: string = 'createDate';
    orderDir: string = 'desc';
    mobile: string = '';
}

// 分页查询返回数据
export interface pageBackDto {
    current: number;
    pages: number;
    size: number;
    total: number;
    records: any[];
}

// 包裹日志项
export interface trackItemDto {
    desc: string;
    time: string;
}

// 返回的日志对象
export interface trackDto {
    cpCode: string;
    fullTraceDetail: trackItemDto[];
    logisticsStatus: string;
    logisticsStatusDesc: string;
    mailNo: string;
}

export interface SearchDto {
    cabinetLocationCode?: string;
    orderType?: number;
    orderSendStatus?: number;
    hasOutbound?: boolean | string;
    checkCode?: string;
    current: number;
    size: number;
}

// 储物订单
export interface keepOrder {
    senderPhone: string,
    keepHour: number,
    appId: string,
    receiverMobile: string,
    boxType: number,
    cabinetLocationCode: string
}

// 品牌编码

export interface billListDto {
    checkCode?: string; // 校验码
    waybillNo?: string; // 运单号
    brandCode?: string; // 品牌编码
}